/** @type {import('next').NextConfig} */
const isProd = process.env.NODE_ENV === 'production';
const basePath = '/solutions-hub';

const nextConfig = {
/* config options here */
  basePath: basePath,
  assetPrefix: isProd ? process.env.NEXT_PUBLIC_SOLUTIONS_HUB_ASSET_PREFIX : '',
  output: 'standalone',
  trailingSlash: true,
  serverRuntimeConfig: {
    captchaSecretKey: process.env.RECAPTCHA_SECRET_KEY,
    captchaVerifyUrl: process.env.GOOGLE_RECAPTCHA_VERIFY_URL,
    emailServiceApi: process.env.EMAIL_SERVICE_API_URL,
    emailServiceApiKey: process.env.EMAIL_SERVICE_API_KEY,
    emailServiceApiSecret: process.env.EMAIL_SERVICE_API_SECRET,
    emailServiceMailTo: process.env.EMAIL_SERVICE_MAIL_TO,
    emailServiceMailFrom: process.env.EMAIL_SERVICE_MAIL_FROM,
    emialServiceTemplateCode:process.env.EMAIL_SERVICE_TEMPLATE_CODE
  },
  async redirects() {
    return [
      {
        source: '/',
        destination: basePath,
        permanent: false,
        basePath: false,
      },
    ];
  },
};

module.exports = nextConfig;
