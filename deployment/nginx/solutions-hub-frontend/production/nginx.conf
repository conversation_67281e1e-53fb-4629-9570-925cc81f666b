user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /var/run/nginx.pid;
daemon off;
#server_tokens off;


events {
    worker_connections 1024;
}

http {
    limit_conn_zone $binary_remote_addr zone=addr:100m;
    server_tokens off;

    # geoip_country        /usr/share/GeoIP/GeoIP.dat;
    # geoip_city           /usr/share/GeoIP/GeoLiteCity.dat;

    log_format  main  '[[$time_local]] - [[$http_x_forwarded_for]] - [[$remote_addr]] - [[$request]] - [[$status]] - [[$request_time]] - [[$bytes_sent]] - [[$body_bytes_sent]] - [[$request_length]] - [[$upstream_addr]] - [[$upstream_status]] - [[$upstream_response_time]] - [[$http_referer]] - [[$http_user_agent]] - [[$remote_user]]';

    log_format healthd '$msec"$uri"'
                '$status"$request_time"$upstream_response_time"'
                '$http_x_forwarded_for';


    sendfile            on;
    tcp_nopush          on;
    tcp_nodelay         on;
    keepalive_timeout   65;
    types_hash_max_size 2048;
    client_max_body_size 4m;
    gzip                on;
    gzip_http_version   1.0;
    gzip_proxied        any;
    gzip_min_length     500;
    gzip_disable        "MSIE [1-6]\.";
    gzip_types          text/plain text/xml text/css
                        text/comma-separated-values
                        text/javascript
                        application/x-javascript
                        application/atom+xml;

    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;

    include /etc/nginx/conf.d/*.conf;
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";
    add_header X-XSS-Protection "1; mode=block";

    index   index.html index.htm;


    upstream solutions-hub-frontend {
        server solutions-hub-frontend-app:3000;
    }

    server {
        listen       80 default_server;
        server_name  <PRODUCTION_DOMAIN>;
        root         /ygag/nginx;
        server_tokens off;


        if ($time_iso8601 ~ "^(\d{4})-(\d{2})-(\d{2})T(\d{2})") {
            set $year $1;
            set $month $2;
            set $day $3;
            set $hour $4;
        }

        access_log  /var/log/nginx/access.log  main;


        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-Content-Type-Options "nosniff";
        add_header X-XSS-Protection "1; mode=block";
        add_header X-Robots-Tag "noindex, nofollow, nosnippet, noarchive";
        add_header 'Cache-Control' 'private';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Max-Age' '1200';
        add_header 'Access-Control-Allow-Headers' 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';

       location /nginx-health {
            return 200;
            break;
       }

       location /favicon.ico {
            return 302  https://cdn.yougotagift.com/static/img/favicon.ico;
        }

       location /robots.txt {
            alias templates/robots.txt;
       }


       location / {
            proxy_set_header Host               $host;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host   $host:443;
            proxy_set_header X-Forwarded-Server $host;
            proxy_set_header X-Forwarded-Port   443;
            proxy_set_header X-Forwarded-Proto "https";

            proxy_read_timeout 180;
            proxy_redirect off;
            proxy_pass http://solutions-hub-frontend;
       }
    }
}
