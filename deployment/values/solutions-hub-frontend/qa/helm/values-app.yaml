namespace: 'solutions-hub-frontend-[JIRA_ID]'

app_name: 'solutions-hub-frontend-[JIRA_ID]'
environment: 'qa'

service:
  name: 'solutions-hub-frontend-[JIRA_ID]-app'
  default:
    port: 3000
    protocol: 'TCP'
    targetPort: 3000

enableHPA: false
hpa:
  name: 'solutions-hub-frontend-[JIRA_ID]-app-hpa'
  minReplicas: 2
  maxReplicas: 2
  targetCPUUtilizationPercentage: 80

enableKedaCron: true
keda:
  name: 'solutions-hub-frontend-[JIRA_ID]-app-keda'
  minReplicas: 1
  desiredReplicas: 2
  maxReplicas: 2
  start: "0 7 * * 1-5"
  end: "0 22 * * 1-5"
  cpu: 80

pdb:
  name: 'solutions-hub-frontend-[JIRA_ID]-app-pdb'
  minAvailable: 50%

deployment:
  name: 'solutions-hub-frontend-[JIRA_ID]-app-deployment'
  replicas: 2
  maxSurge: 100%
  maxUnavailable: 50%
  serviceAccountName: 'ygag-solutions-hub-frontend-vault'

  containers:
    default:
      name: 'app'
      imagePullPolicy: 'IfNotPresent'
      image: '************.dkr.ecr.me-central-1.amazonaws.com/qa/ygg/solutions-hub/frontend-app:[BUILD_TAG]'
      command: '["sh", "-c" , "source /etc/profile.d/application-env.sh; node server.js"]'
      port: 3000
      memory:
        requests: 300Mi
        limits: 400Mi
      cpu:
        requests: 100m
      health:
        path: '/solutions-hub/health/'
        port: 3000
        scheme: 'HTTP'
      startupProbe:
        initialDelaySeconds: 10
        periodSeconds: 5
        timeoutSeconds: 5
        successThreshold: 1
        failureThreshold: 7
      readinessProbe:
        initialDelaySeconds: 0
        periodSeconds: 15
        timeoutSeconds: 5
        successThreshold: 1
        failureThreshold: 3
      livenessProbe:
        initialDelaySeconds: 0
        periodSeconds: 15
        timeoutSeconds: 5
        successThreshold: 1
        failureThreshold: 4
      volumeMounts:
        - mountPath: '/ygag/logs/'
          name: 'ygag-solutions-hub-frontend-[JIRA_ID]-qa-app-logs'
        - name: 'solutions-hub-frontend-app-env-volume'
          mountPath: "/vault/secrets"

  volumes:
    - name: 'ygag-solutions-hub-frontend-[JIRA_ID]-qa-app-logs'
      hostPath:
        path: '/home/<USER>/ygag-logs/ygag-solutions-hub-frontend-[JIRA_ID]-qa/app'
    - name: 'solutions-hub-frontend-app-env-volume'
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: "solutions-hub-frontend-envs"

  nodeSelector:
    key: 'karpenter.sh/nodepool'
    value: 'default'

  topologySpreadConstraints:
    - maxSkew: 2
      topologyKey: 'topology.kubernetes.io/zone'
      whenUnsatisfiable: DoNotSchedule
      labelSelector:
        matchLabels:
          app: 'solutions-hub-frontend-[JIRA_ID]'
          tier: app
    - maxSkew: 2
      topologyKey: 'kubernetes.io/hostname'
      whenUnsatisfiable: DoNotSchedule
      labelSelector:
        matchLabels:
          app: 'solutions-hub-frontend-[JIRA_ID]'
          tier: app

  priorityClassName: 'qa-medium'
  terminationGracePeriodSeconds: 60
