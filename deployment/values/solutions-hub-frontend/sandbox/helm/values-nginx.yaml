namespace: 'solutions-hub-frontend'

app_name: 'solutions-hub-frontend'
environment: 'sandbox'

service:
  name: 'solutions-hub-frontend-nginx'
  default:
    port: 80
    protocol: 'TCP'
    targetPort: 80
  https:
    port: 443
    protocol: 'TCP'
    targetPort: 80

hpa:
  name: 'solutions-hub-frontend-nginx-hpa'
  minReplicas: 3
  maxReplicas: 5
  targetCPUUtilizationPercentage: 80

pdb:
  name: 'solutions-hub-frontend-nginx-pdb'
  minAvailable: 50%

deployment:
  name: 'solutions-hub-frontend-nginx-deployment'
  replicas: 3
  maxSurge: 100%
  maxUnavailable: 0%

  containers:
    default:
      name: 'nginx'
      imagePullPolicy: 'Always'
      image: '459037613883.dkr.ecr.me-central-1.amazonaws.com/production/ygg/solutions-hub/frontend-nginx:[BUILD_TAG]'
      port: 80
      memory:
        requests: 32Mi
        limits: 64Mi
      cpu:
        requests: 10m
        limits: 100m
      health:
        path: '/nginx-health'
        port: 80
        scheme: 'HTTP'
      startupProbe:
        initialDelaySeconds: 3
        periodSeconds: 5
        timeoutSeconds: 2
        successThreshold: 1
        failureThreshold: 5
      readinessProbe:
        initialDelaySeconds: 0
        periodSeconds: 5
        timeoutSeconds: 2
        successThreshold: 1
        failureThreshold: 3
      livenessProbe:
        initialDelaySeconds: 0
        periodSeconds: 5
        timeoutSeconds: 2
        successThreshold: 1
        failureThreshold: 4

  volumes:
    local:
      - name: 'ygg-solutions-hub-frontend-sandbox-nginx-logs'
        mountPath: '/var/log/nginx/'
        hostPath: '/home/<USER>/ygag-logs/ygg-solutions-hub-frontend-sandbox/nginx'

  nodeSelector:
    key: 'karpenter.sh/nodepool'
    value: 'default'

  topologySpreadConstraints:
    - maxSkew: 1
      topologyKey: 'topology.kubernetes.io/zone'
      whenUnsatisfiable: DoNotSchedule
      labelSelector:
        matchLabels:
          app: 'solutions-hub-frontend'
          tier: nginx
    - maxSkew: 1
      topologyKey: 'kubernetes.io/hostname'
      whenUnsatisfiable: DoNotSchedule
      labelSelector:
        matchLabels:
          app: 'solutions-hub-frontend'
          tier: nginx

  priorityClassName: 'sandbox-medium'
  terminationGracePeriodSeconds: 100
