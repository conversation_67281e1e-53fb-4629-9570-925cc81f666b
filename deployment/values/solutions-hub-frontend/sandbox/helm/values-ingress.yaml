ingress:
  apiVersion: 'networking.k8s.io/v1'

  namespace: 'solutions-hub-frontend'
  name: 'solutions-hub-frontend-ingress'

  annotations:
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/backend-protocol: 'HTTP'
    alb.ingress.kubernetes.io/certificate-arn: '<SANDBOX_CERTIFICATE_ARN>'
    alb.ingress.kubernetes.io/group.name: 'default'
    alb.ingress.kubernetes.io/healthcheck-path: '/solutions-hub/health/'
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/scheme: 'internet-facing'
    alb.ingress.kubernetes.io/tags: 'Name=ygg-sandbox-default-alb-eks-tf, Platform=EKS, Environment=sandbox'
    alb.ingress.kubernetes.io/wafv2-acl-arn: 'arn:aws:wafv2:me-central-1:843554671018:regional/webacl/AWSRegionalWAF2/b854d487-34ca-4786-9270-500fedcbb502'
    alb.ingress.kubernetes.io/target-type: 'ip'
    alb.ingress.kubernetes.io/subnets: 'ygg-sand-public-me-central-1a, ygg-sand-public-me-central-1b, ygg-sand-public-me-central-1c'
    alb.ingress.kubernetes.io/ssl-policy: 'ELBSecurityPolicy-TLS-1-2-2017-01'
    alb.ingress.kubernetes.io/load-balancer-attributes: 'idle_timeout.timeout_seconds=120'
    kubernetes.io/ingress.class: 'alb'

  rules:
    - host: '<SANDBOX_DOMAIN>'
      http:
        paths:
          - backend:
              service:
                name: 'solutions-hub-frontend-nginx'
                port:
                  number: 80
            path: '/*'
            pathType: 'ImplementationSpecific'
