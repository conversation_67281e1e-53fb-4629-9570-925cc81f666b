namespace: 'solutions-hub-frontend'

app_name: 'solutions-hub-frontend'
environment: 'sandbox'

service:
  name: 'solutions-hub-frontend-app'
  default:
    port: 3000
    protocol: 'TCP'
    targetPort: 3000

hpa:
  name: 'solutions-hub-frontend-app-hpa'
  minReplicas: 3
  maxReplicas: 5
  targetCPUUtilizationPercentage: 80

pdb:
  name: 'solutions-hub-frontend-app-pdb'
  minAvailable: 50%

deployment:
  name: 'solutions-hub-frontend-app-deployment'
  replicas: 3
  maxSurge: 100%
  maxUnavailable: 0%
  serviceAccountName: 'ygg-solutions-hub-frontend-vault'

  containers:
    default:
      name: 'app'
      imagePullPolicy: 'Always'
      image: '************.dkr.ecr.me-central-1.amazonaws.com/production/ygg/solutions-hub/frontend-app:[BUILD_TAG]'
      command: '["sh", "-c" , "source /vault/secrets/application.env; node server.js"]'
      port: 3000
      memory:
        requests: 400Mi
        limits: 500Mi
      cpu:
        requests: 100m
      health:
        path: '/solutions-hub/health/'
        port: 3000
        scheme: 'HTTP'
      startupProbe:
        initialDelaySeconds: 10
        periodSeconds: 5
        timeoutSeconds: 5
        successThreshold: 1
        failureThreshold: 7
      readinessProbe:
        initialDelaySeconds: 0
        periodSeconds: 15
        timeoutSeconds: 5
        successThreshold: 1
        failureThreshold: 3
      livenessProbe:
        initialDelaySeconds: 0
        periodSeconds: 15
        timeoutSeconds: 5
        successThreshold: 1
        failureThreshold: 4
      volumeMounts:
        - mountPath: '/ygag/logs/'
          name: 'ygg-solutions-hub-frontend-sandbox-app-logs'
        - name: 'solutions-hub-frontend-app-env-volume'
          mountPath: "/vault/secrets"

  volumes:
    - name: 'ygg-solutions-hub-frontend-sandbox-app-logs'
      hostPath:
        path: '/home/<USER>/ygag-logs/ygg-solutions-hub-frontend-sandbox/app'
    - name: 'solutions-hub-frontend-app-env-volume'
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: "solutions-hub-frontend-envs"

  nodeSelector:
    key: 'karpenter.sh/nodepool'
    value: 'default'

  topologySpreadConstraints:
    - maxSkew: 1
      topologyKey: 'topology.kubernetes.io/zone'
      whenUnsatisfiable: DoNotSchedule
      labelSelector:
        matchLabels:
          app: 'solutions-hub-frontend'
          tier: app
    - maxSkew: 1
      topologyKey: 'kubernetes.io/hostname'
      whenUnsatisfiable: DoNotSchedule
      labelSelector:
        matchLabels:
          app: 'solutions-hub-frontend'
          tier: app

  priorityClassName: 'sandbox-medium'
  terminationGracePeriodSeconds: 60
