## Stage: Dependency Install ##
FROM node:20-alpine AS deps

# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat

WORKDIR /ygag
ENV NEXT_TELEMETRY_DISABLED=1

ARG NPM_AUTH_TOKEN

ADD ./package-lock.json /ygag/package-lock.json
ADD ./package.json /ygag/package.json

# Set up npm registry for internal package installation
RUN npm config set @yougotagift:registry=https://npm.pkg.github.com/ && npm config set //npm.pkg.github.com/:_authToken=$NPM_AUTH_TOKEN

ADD ./application-env.sh /etc/profile.d/application-env.sh
RUN source /etc/profile.d/application-env.sh ; npm install --frozen-lockfile

## Stage: Node Builder ##
FROM node:20-alpine as builder

WORKDIR /ygag
ENV NEXT_TELEMETRY_DISABLED=1

COPY --from=deps /ygag/node_modules /ygag/node_modules
COPY --from=deps /etc/profile.d/application-env.sh /etc/profile.d/application-env.sh

# Sources
ADD ./ /ygag/
ADD ./package-lock.json /ygag/package-lock.json
ADD ./package.json /ygag/package.json

RUN source /etc/profile.d/application-env.sh ; npm run build

## Stage: Static files syncing ##
FROM amazon/aws-cli:2.2.5 as mediasync

WORKDIR /ygag

# Sources from builder
COPY --from=builder /ygag/public /ygag/
COPY --from=builder /ygag/.next/static /ygag/_next/static
COPY --from=builder /etc/profile.d/application-env.sh /etc/profile.d/application-env.sh

# S3 sync
RUN source /etc/profile.d/application-env.sh ; \
    aws s3 sync /ygag $AWS_FRONTEND_BUCKET_URI_PATH --exact-timestamps --region $AWS_REGION

# Cloudfront invalidation
RUN source /etc/profile.d/application-env.sh ; \
    INVALIDATION_ID=`AWS_MAX_ATTEMPTS=10 aws cloudfront create-invalidation --distribution-id $AWS_FRONTEND_CLOUDFRONT_DISTRIBUTION_ID --paths "/*" \
        | grep -oP '"Id":\s*\"\K[^\s,]*(?=\"\s*,)'` ; \
    aws cloudfront wait invalidation-completed \
        --distribution-id $AWS_FRONTEND_CLOUDFRONT_DISTRIBUTION_ID \
        --id $INVALIDATION_ID

RUN touch /docker-multi-stage-build-dependency

## Stage: Final Image Build ##
FROM node:20-alpine

WORKDIR /ygag
ENV NODE_ENV=production
ENV PORT=3000
ENV NEXT_TELEMETRY_DISABLED=1

RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Sources from builder
COPY --from=mediasync /docker-multi-stage-build-dependency /docker-multi-stage-build-dependency
COPY --from=builder /ygag/public /ygag/public
COPY --from=builder /ygag/next.config.js /ygag/next.config.js
COPY --from=builder /ygag/package.json /ygag/package.json
COPY --from=builder --chown=nextjs:nodejs /ygag/.next/standalone /ygag

USER nextjs

EXPOSE 3000
