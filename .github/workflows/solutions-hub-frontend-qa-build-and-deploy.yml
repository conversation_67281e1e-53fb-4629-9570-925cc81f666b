name: QA Build & Deploy solutions-hub-frontend
run-name: Deploying QA Branch ${{ inputs.branch }}

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch'
        required: true
        type: string

  repository_dispatch:
    types: [ "QA Build & Deploy solutions-hub-frontend" ]
    inputs:
      branch:
        description: 'Branch'
        required: true
        type: string

jobs:

  deploy:
    name: QA
    uses: YouGotaGift/devops-organization-actions/.github/workflows/qa-frontend-application-build-and-deploy.yml@main
    with:
      application_name: solutions-hub-frontend
      app_image_repository: solutions-hub
      branch: ${{ inputs.branch || github.event.client_payload.branch }}
    secrets: inherit
