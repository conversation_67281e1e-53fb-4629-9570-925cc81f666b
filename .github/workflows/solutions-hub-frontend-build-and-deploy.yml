name: Build & Deploy solutions-hub-frontend
run-name: Deploying solutions-hub-frontend ${{ inputs.environment }}

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment'
        required: true
        default: 'sandbox'
        type: choice
        options:
        - sandbox
        - production

jobs:

  deploy:
    name: solutions-hub-frontend-${{ inputs.environment }}
    uses: YouGotaGift/devops-organization-actions/.github/workflows/me-central-1-frontend-application-build-and-deploy.yaml@main
    with:
      application_name: solutions-hub-frontend
      environment: ${{ inputs.environment }}
      app_image_repository: solutions-hub
    secrets: inherit
