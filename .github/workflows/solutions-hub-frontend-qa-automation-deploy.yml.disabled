name: QA Automation Deploy solutions-hub-frontend

on:
  schedule:
    - cron: '0 18 * * *'

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Trigger QA Build & Deploy solutions-hub-frontend workflow
        run: |
          curl -X POST \
            -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" \
            -H "Accept: application/vnd.github.everest-preview+json" \
            "https://api.github.com/repos/${{ github.repository }}/dispatches" \
            -d '{"event_type": "QA Build & Deploy solutions-hub-frontend", "client_payload": { "branch": "<QA_AUTOMATION_BRANCH_NAME>", "action": "deploy" }}'

