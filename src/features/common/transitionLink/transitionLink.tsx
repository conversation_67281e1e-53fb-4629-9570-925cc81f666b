"use client";

import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

interface TransitionLinkProps extends LinkProps {
	href: string;
	children: React.ReactNode;
	className?: string;
}

export default function TransitionLink({
	href,
	children,
	...props
}: TransitionLinkProps) {
	const router = useRouter();

	async function handleNavigation(
		e: React.MouseEvent<HTMLAnchorElement, MouseEvent>
	) {
		e.preventDefault();

		const allElement = document.body.getElementsByClassName('all-card-container');
        if(allElement && allElement.length > 0) {
            const elementToAnimate = allElement[0];
            elementToAnimate.classList.add("exit-animation");
            await new Promise((res) => setTimeout(res, 500));
            elementToAnimate.classList.remove("exit-animation");
            router.push(href);
            elementToAnimate.classList.add("enter-animation");
            setTimeout(() => {
                elementToAnimate.classList.remove("enter-animation");
            }, 500);
        } else {
            router.push(href);
        }
	}

	return (
		<Link onClick={handleNavigation} href={href} {...props}>
			{children}
		</Link>
	);
}