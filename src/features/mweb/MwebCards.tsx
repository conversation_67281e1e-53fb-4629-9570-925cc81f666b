import React from 'react'
import styles from './LandingMweb.module.scss';
import ForBlock from 'components/product/forBlock/forBlock';

const MwebCards = ({ title }: { title: string }) => {
    return (
        <div className={styles["mweb-cards"]}>
            <div className={styles["mweb-cards__title"]}>
                <span className={styles["mweb-cards__title-dot"]} />
                <p>{title}</p>
            </div>
            <ForBlock isMweb={true} customerType='brand' />
        </div>
    )
}

export default MwebCards