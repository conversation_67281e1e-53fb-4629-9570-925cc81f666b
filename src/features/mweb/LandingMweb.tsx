"use client";
import { imageBaseUrl } from '@constants/envVariables'
import React from 'react'
import styles from './LandingMweb.module.scss'
import { useTranslation } from 'next-i18next'
import MwebBanner from './MwebBanner';
import MwebHero from "./MwebHero";
import MwebCards from './MwebCards';

const LandingMweb = () => {
  const { t } = useTranslation();
  const logo = `${imageBaseUrl}/images/logo.svg`;
  const cube = `${imageBaseUrl}/images/cube.svg`;
  const mwebMsg = `${imageBaseUrl}/images/mweb-msg.svg`;
  return (
    <div className={styles["mweb-landing"]}>
      <MwebHero />
      <div className={styles["mweb-header"]}>
        <img src={logo} alt='yougotagift-logo' height={30} />
        <div className={styles["mweb-header__divider"]}></div>
        <div className={styles["mweb-header__solution"]}>
          <img src={cube} alt='solutionHub-logo' width={13.8} height={13.8} />
          <span>{t("solutionHub")}</span>
        </div>
      </div>
      <div className={styles["mweb-banner-wrap"]}>
        <MwebBanner />
      </div>
      <div className={styles["mweb-card-wrap"]}>
        <MwebCards title={t("buyersLabel")} />
        <MwebCards title={t("brandsLabel")} />
      </div>
      <div className={styles["mweb-msg"]}>
        <img src={mwebMsg} alt='msg' height={24} width={24} />
        <p>{t("mwebMsg")}</p>
      </div>
    </div>
  );
}

export default LandingMweb