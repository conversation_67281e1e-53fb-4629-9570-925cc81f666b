'use client'

import React from 'react'
import styles from "./LandingMweb.module.scss";
import { useTranslation } from 'next-i18next';

const MwebHero = () => {
      const { t } = useTranslation();
  return (
    <div className={styles["mweb-hero"]}>
        <div className={styles["mweb-hero__tag"]}>NEW</div>
        <div className={styles["mweb-hero__list"]}>
          <div className={styles["mweb-hero__marquee"]}>
            <span className={styles["mweb-hero__item"]}>{t("newtWork")}</span>
            <span className={styles["mweb-hero__separator"]}>•</span>
            <span className={styles["mweb-hero__item"]}>{t("clients")}</span>
            <span className={styles["mweb-hero__separator"]}>•</span>
            <span className={styles["mweb-hero__item"]}>
              {t("majorPrograms")}
            </span>
          </div>
        </div>
      </div>
  )
}

export default MwebHero