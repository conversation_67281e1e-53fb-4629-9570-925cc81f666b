'use client'

import { imageBaseUrl } from '@constants/envVariables';
import AnimatedTitleWithSubtitle from 'components/common/animatedTitleWithSubtitle/AnimatedTitleWithSubtitle';
import React from 'react'
import { useTranslation } from 'next-i18next';
import styles from './LandingMweb.module.scss';

const MwebBanner = () => {
      const { t, i18n } = useTranslation();
    
      const backgroundImage = `${imageBaseUrl}/images/solutionHubBg.png`;
      const globe = `${imageBaseUrl}/images/globe.png`;
      const locale = i18n.language;
    
      const subTitles = [t("bannerSubTitle1"), t("bannerSubTitle2")];
  return (
    <>
    <style jsx>{`
        .banner {
          background: url("${backgroundImage}");
          background-repeat: no-repeat;
          background-size: cover;
        }
        .blobe {
          background: url("${globe}");
          background-repeat: no-repeat;
          background-position-y: 10px;
          background-position-x: center;
          background-size: 150%;
        }
      `}</style>
      <div className={`banner ${styles["banner"]}`}>
        <div className={`blobe ${styles["banner__container"]}`}>
          <AnimatedTitleWithSubtitle
            title={t("solutionHub")}
            subtitle={subTitles}
            locale={locale}
            isMweb={true}
          />
        </div>
      </div>
      </>
  )
}

export default MwebBanner