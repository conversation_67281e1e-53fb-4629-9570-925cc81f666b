import { CountryCode } from "libphonenumber-js/min";
import { isValidPhoneNumber as checkPhonenumberValidOrNot } from "react-phone-number-input/input-max";

const isValidPhoneNumber = (phoneNumber: string, dialCode: any) => {
  const formattedDialCode = String(dialCode).startsWith("+") ? dialCode : `+${dialCode}`;
  return checkPhonenumberValidOrNot(formattedDialCode + phoneNumber);
};

export default isValidPhoneNumber;
