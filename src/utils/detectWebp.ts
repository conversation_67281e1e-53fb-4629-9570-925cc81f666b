/**
 * The function `supportWebP` checks if the browser supports the WebP image format using a Promise.
 * @returns The `supportWebP` function returns a Promise that resolves to a boolean value indicating
 * whether the browser supports the WebP image format.
 */
export function supportWebP(): Promise<boolean> {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => {
      resolve(img.width > 0 && img.height > 0);
    };
    img.onerror = () => {
      resolve(false);
    };
    img.src =
      "data:image/webp;base64,UklGRhIAAABXRUJQVlA4TAYAAAAvAAAAAAfQ//73v/+BiOh/AAA=";
  });
}
