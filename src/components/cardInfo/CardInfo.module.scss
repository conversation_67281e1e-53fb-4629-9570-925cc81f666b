@import "@styles/variables";
@import "@styles/mixins";

.card {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 778px;
    height: 612px;

    border-radius: 24px 24px 24px 24px;
    border: 2px solid rgba(194, 184, 255, 0.10);
    background: rgb(19 7 45 / 50%);

    &__header {
        height: 91px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 24px;

        h2 {
            color:  #B800C4;
            font-family: $bricolage-font-family;
            font-optical-sizing: none;
            font-size: 24px;
            font-style: normal;
            font-weight: 800;
            line-height: 91px;

            span{
                font-family: $default-font-family !important;
            }

            @include rtl-styles{
                font-family: $arabic-font-family;
            }
        }
        border-bottom: 2px solid rgba(194, 184, 255, 0.10);

        &-bubble{
            display: flex;
            padding: 10px 16px;
            justify-content: center;
            align-items: center;
            border-radius: 45px;
            border: 1px solid #8679D5;
            background: #2B343F;
            gap: 10px;
            font-size: 12px;
            font-weight: 500;
            line-height: 18px;
            letter-spacing: -0.12px;
            color: #99C6FF;
        }
    }

    &__body {
        height: 520px;
        width: 100%;
        display: flex;
        overflow: hidden;

        &--left{
            display: flex;
            max-width: 211px;
            width: 100%;
            padding: 26px 16px 170px 16px;
            flex-direction: column;
            align-items: center;
            gap: 16px;
        }
        &--right{
            display: flex;
            width: 100%;
            flex-direction: column;
            align-items: center;
            border-left: .5px solid #3c3c3c; 

            @include rtl-styles {
                border-left: none;
                border-right: .5px solid #3c3c3c; 
            }
        }
    }
}

.right-section{
    &__header{
        margin: 40px 24px 24px 24px;
        min-width: 514px;
        display: flex;
        flex-direction: column;
        gap: 4px;

        h2{
            color: #B800C4;
            /* Heading/H2 */
            font-family:var(--font-bricolage);
            font-size: 24px;
            font-style: normal;
            font-weight: 800;
            line-height: 32px; /* 133.333% */
            // direction: ltr;


            span{
                font-family: $default-font-family !important;
            }

            @include rtl-styles{
                font-family: $arabic-font-family;
                direction: ltr;
                text-align: right;
            }
        }
        p{
            color: #C2B8FF;
            /* Body/B4 - Regular */
            font-family: var(--font-mona-sans);
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: 18px; /* 150% */
            letter-spacing: -0.12px;

            @include rtl-styles {
                font-family: $arabic-font-family;
              }
        }
        &--chips{
            display: flex;
            padding: 24px 0px 24px 0px;
            border-bottom: 0.5px solid #483C6C;
        }
    }
    &__content{
        display: flex;
        height: 370px;
        padding: 0px 24px 24px 24px;
        flex-direction: column;
        align-items: flex-start;
        gap: 24px;
        flex-shrink: 0;
        align-self: stretch;
        max-width: 550px;

        section{
            width: 100%;
        }
    }
}

.titleNSlider-container{
    display: flex;
    flex-direction: column;
    gap: 32px;
}

.brand-network-container{
    display: flex;
    flex-direction: column;
}