import { useTranslation } from "next-i18next";
import styles from './titleNSlider.module.scss';
import Slider from "components/common/slider/Slider";

const TitleNSlider = ({title, slideData}: any) => {
    const { t } = useTranslation();

    return (
        <div className={styles['titleNChips']}>
            <h2 className={styles['titleNChips__title']}>
                {t(title)}
            </h2>
            <Slider data={slideData} />
        </div>
    )
}

export default TitleNSlider;