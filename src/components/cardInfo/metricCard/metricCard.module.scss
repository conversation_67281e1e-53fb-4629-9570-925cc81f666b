@import "@styles/variables";
@import "@styles/mixins";

.metric-card {
    display: flex;
    gap: 16px;
    max-width: fit-content;
    min-width: fit-content;
    width: 100%;
    height: 72px;
    padding: 16px;
    border-radius: 12px;
    background: #0B0219;
    box-shadow: 0px 0px 47px 0px rgba(255, 255, 255, 0.05) inset;

    img{
        width: 40px;
        height: 40px;
    }
}

.content {
    display: flex;
    flex-direction: column;

    &__title {
        color: #C2B8FF;
        text-shadow: 0px 0.5px 0.5px rgba(0, 0, 0, 0.10);
        /* Body/B2 - Semi bold */
        font-family: "Mona Sans";
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px; /* 150% */
        width: fit-content;

        @include rtl-styles {
            font-family: $arabic-font-family;
          }
    }

    &__subtitle {
        color: #C2B8FF;
        text-shadow: 0px 0.5px 0.5px rgba(0, 0, 0, 0.10);
        /* Body/B4 - Regular */
        font-family: "Mona Sans";
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        line-height: 18px; /* 150% */
        letter-spacing: -0.12px;
        display: inline-block;
        // min-width: 200px;
        width: fit-content;

        @include rtl-styles {
            font-family: $arabic-font-family;
          }
    }
}

.metric-card-wrapper{
    background: linear-gradient(150deg, #B800C4, #0B0219);
    border-radius: 12px;
    padding: 0.5px;
    max-width: fit-content;
    width: 100%;
    min-width: fit-content;
}