import { imageBaseUrl } from "@constants/envVariables";
import styles from './metricCard.module.scss';
import { useTranslation } from "next-i18next";

const MetricCard = ({title, subtitle, image}: any) => {
    const { t } = useTranslation();

    return (
        <div className={styles['metric-card-wrapper']}>
            <div className={styles['metric-card']}>
                <div>
                    <img src={`${imageBaseUrl}/images/${image}`} alt="Metric Card" />
                </div>
                <div className={styles['content']}>
                    <span className={styles['content__title']}>{t(title)}</span>
                    <span className={styles['content__subtitle']}>{t(subtitle)}</span>
                </div>
            </div>
        </div>

    )
}

export default MetricCard;