import { useTranslation } from "next-i18next";
import Chips from "../chips/Chips";
import styles from './titleNChips.module.scss';

const TitleNChips = ({title, chips}: any) => {
    const { t } = useTranslation();

    return (
        <div className={styles['titleNChips']}>
            <h2 className={styles['titleNChips__title']}>
                {t(title)}
            </h2>
            <Chips list={chips} />
        </div>
    )
}

export default TitleNChips;