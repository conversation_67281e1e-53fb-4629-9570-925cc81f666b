import React from "react";
import styles from "./ImageContainer.module.scss";
import { imageBaseUrl } from "@constants/envVariables";
import { useTranslation } from "next-i18next";
import Image from "next/image";

const ImageContainer = ({ image, name }) => {
  const { t } = useTranslation();

  return (
    <>
      {t(name) === t("customDesigns") && (
        <div className={styles["image-container"]}>
          <img
            src={`${imageBaseUrl}/images/${image}`}
            alt="Image"
            width={307}
            height={237}
          />
        </div>
      )}

      {t(name) === t("asistedSales") && (
        <img
          src={`${imageBaseUrl}/images/${image}`}
          alt="Image"
          style={{ display: "block", width: "100%", height: "100%" }}
        />
      )}

      {t(name) === t("atWorkPro") && (
        <img
          src={`${imageBaseUrl}/images/${image}`}
          alt="Image"
          style={{
            display: "block",
            width: "100%",
            height: "85%",
            marginTop: "32px",
          }}
        />
      )}

      {t(name) !== t("atWorkPro") &&
        t(name) !== t("asistedSales") &&
        t(name) !== t("customDesigns") && (
          <div className={styles[name]}>
            <Image
              src={`${imageBaseUrl}${image}`}
              alt={name}
              width={183}
              height={100}
            />
          </div>
        )}
    </>
  );
};

export default ImageContainer;
