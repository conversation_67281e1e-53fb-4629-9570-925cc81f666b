import { imageBaseUrl } from "@constants/envVariables";
import styles from "./companyLogo.module.scss";

const CompanyLogo = () => {
    return (
        <div className={styles['client-partner']}>
            <img src={`${imageBaseUrl}/images/logo/emirates.png`} alt="Client Partners" />
            <img src={`${imageBaseUrl}/images/logo/visa.png`} alt="Client Partners" />
            <img src={`${imageBaseUrl}/images/logo/schneider.png`} alt="Client Partners" />
            <img src={`${imageBaseUrl}/images/logo/sabic.png`} alt="Client Partners" />
            <img src={`${imageBaseUrl}/images/logo/mastercard.png`} alt="Client Partners" />
            <img src={`${imageBaseUrl}/images/logo/acwa.png`} alt="Client Partners" />
            <img src={`${imageBaseUrl}/images/logo/sab.png`} alt="Client Partners" />
        </div>
    )
}

export default CompanyLogo;