import { useTranslation } from 'next-i18next';
import styles from './Chips.module.scss'
const Chips = ({ list }: any) => {
    console.log('list', list);
    const { t } = useTranslation();

    return (
        <div className={styles['chips']}>
            {
                list?.map((item: any, index: number) => {
                    return (
                        <div className={styles['chips-wrapper']} key={index}>
                            <div className={styles['chips__chip']} >
                                <span>{t(item)}</span>
                            </div>
                        </div>
                    )
                })
            }
        </div>
    )
}
export default Chips;