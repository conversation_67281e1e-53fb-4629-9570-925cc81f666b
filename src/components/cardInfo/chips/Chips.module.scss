@import "@styles/variables";
@import "@styles/mixins";

.chips{
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
       
    &__chip{
        display: flex;
        padding: 7px 16px;
        flex-direction: column;
        align-items: center;
        gap: 16px;
        border-radius: 100px;
        // border: 0.5px solid #B800C4;
        background: #0B0219;
        box-shadow: 0px 0px 7px 0px rgba(49, 107, 255, 0.25), 0px 0px 47px 0px rgba(49, 107, 255, 0.09) inset;
    
        span{
            color: #B800C4;
            /* Body/B4 - Regular */
            font-family: "Mona Sans";
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: 18px; /* 150% */
            letter-spacing: -0.12px;

            @include rtl-styles {
                font-family: $arabic-font-family;
              }
        }
    }
}

.chips-wrapper{
    background: linear-gradient(150deg, #B800C4, #000);
    padding: 0.5px;
    border-radius: 100px;
}
