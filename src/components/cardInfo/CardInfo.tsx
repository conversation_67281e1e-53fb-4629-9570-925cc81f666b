'use client';

import CardFeatureTile from 'components/cardFeatureTile/CardFeatureTile';
import styles from './CardInfo.module.scss';
import Header from './header/Header';
import Chips from './chips/Chips';
import TitleNChips from './TitleNChips/titlenChips';
import { getTranslation } from 'app/[lang]/getTranslation';
import { useTranslation } from 'next-i18next';
import { useEffect, useState } from 'react';
import { FEATURE, PRODUCT } from '@constants/common';
import Divider from 'components/common/divider/Divider';
import CompanyLogo from './companyLogo/companyLogo';
import Slider from 'components/common/slider/Slider';
import { atWorkUrl, imageBaseUrl } from '@constants/envVariables';
import TitleNSlider from './TitleNSlider/titlenNSlider';
import ImageContainer from './imageContainer/ImageContainer';
import MetricCard from './metricCard/metricCard';
import TitleNImage from './titleNImage/titleNImage';
import Cards from 'components/home/<USER>/cards/Cards';
import PointEngine from './pointEngine/pointEngine';

const getFeatureContent = (data : any, slug: string, clickedTile: number) => {
    const productInfo = data?.features?.filter((item: any) => item?.id == clickedTile);
    if(slug){
        const featureInfo = productInfo[0]?.features?.filter((item: any) => item?.slug == slug);
        if(featureInfo && featureInfo.length > 0){
            return featureInfo[0];
        } else {
            return productInfo[0];
        }
    }
    return productInfo[0];
}

const CardInfo = ({ data, tileId }: any) => {
    console.log('data', data);

    const { t } = useTranslation();
    const [clickedTile, setClickedTile] = useState(tileId);
    const [content, setContent] = useState(data?.features?.filter((item: any) => item?.id == clickedTile)[0]);

    useEffect(() => {
        setContent(data?.features?.filter((item: any) => item?.id == clickedTile)[0]);
    }, [clickedTile]);


    const isRelevantProduct =
      data?.id === PRODUCT.GIFT_CARD_API || data?.id === PRODUCT.AT_WORK || PRODUCT.GIFTING_WALLET || PRODUCT.CARD_PRODUCTS || PRODUCT.CAMPAIGN_MANAGEMENT;

    const isRelevantFeature =
      content?.id === FEATURE.GUIDED_EXPERIENCE ||
      content?.id === FEATURE.BUSINESS ||
      content?.id === FEATURE.BULK_ORDER ||
      content?.id === FEATURE.WORK_PRO ||
      content?.id === FEATURE.BRAND_NETWORK ||
      content?.id === FEATURE.GIFT_CATALOG ||
      content?.id === FEATURE.WALLET ||
      content?.id === FEATURE.CONGIGURABLE_PRMOTION ||
      content?.id === FEATURE.CATEGORY_SPECIFIC_CAMPAIGN ||
      content?.id === FEATURE.CONDITIONAL_VOUCHERS ||
      content?.id === FEATURE.LOYALTY_POINTS
       ;

    const CampaignManagementUI = (content: any) => {
        return (
            <>
                {
                    content?.id == FEATURE.CONGIGURABLE_PRMOTION && <Chips list={content?.body?.chips} />
                }
            </>
        )
    }

    const ValueAddedUI = (content: any) => {
        return (
            <>
                {
                    content?.id == FEATURE.WHITE_LABEL && <Chips list={content?.body?.chips} />
                }
            </>
        )
    }




    return (
        <div className={styles['card']}>
            <div className={styles['card__header']}>
                <h2>{ data?.id == PRODUCT.AT_WORK ? `@${t(data?.name)}` : t(data?.name) }</h2>
                {data?.id == PRODUCT.AT_WORK && 
                <a className={styles['card__header-bubble']} href={atWorkUrl} target='_blank'>
                    {t("exploreMore")}
                    <img src={`${imageBaseUrl}/images/work-icon.svg`} alt="" width={24} height={24}/>
                </a>}
            </div>
            <div className={styles['card__body']}>
                <div className={styles['card__body--left']}>
                    {
                        data?.features.map((item: any, index: number) => {
                            return (
                                <CardFeatureTile id={item?.id} icon={clickedTile == item?.id ? `${imageBaseUrl}/images/${item.icon}` : `${imageBaseUrl}/images/${item['icon-inactive']}`} key={index} title={t(item?.name)} className={clickedTile == item?.id ? 'active' : ''} callbackTileClick={setClickedTile} />
                            )
                        })
                    }
                </div>
                <div className={`${styles['card__body--right']} component-transition`} key={clickedTile}>
                    <div className={styles['right-section']}>
                        <div className={styles['right-section__header']}>
                            <Header title={content?.body?.title === "atWorkPro" ? <>
                                <span>@</span>
                                {t(content?.body?.title)}
                                </>: t(content?.body?.title)} subTitle={t(content?.body?.subTitle)} />
                            {
                                data?.id == PRODUCT.GIFT_CARD_API && content?.id == FEATURE.BRAND_NETWORK && <div className={styles['right-section__header--chips']}>
                                    <Chips list={content?.body?.chips1} />
                                </div>
                            }
                        </div>
                        <div className={styles['right-section__content']}>
                            <section>
                                {
                                    data?.id == PRODUCT.GIFT_CARD_API && content?.id == FEATURE.TECHNOLOGY && <div className={styles['titleNSlider-container']}>
                                        <TitleNSlider title={content?.body?.performaceUptime?.title} slideData={content?.body?.performaceUptime?.slideData} />
                                        <TitleNSlider title={content?.body?.security?.title} slideData={content?.body?.security?.slideData} />
                                    </div>
                                }
                                {
                                    data?.id == PRODUCT.GIFT_CARD_API && content?.id == FEATURE.USE_CASE && <>
                                        <TitleNChips title={content?.body?.chipsNTitle1?.title} chips={content?.body?.chipsNTitle1?.chips} />
                                        <Divider />
                                        <TitleNChips title={content?.body?.chipsNTitle2?.title} chips={content?.body?.chipsNTitle2?.chips} />
                                    </>
                                }
                                {isRelevantProduct && isRelevantFeature && (
                                    <Chips list={content?.body?.chips} />
                                )} 
                                {
                                    content?.body?.image && <ImageContainer image={content?.body?.image} name={content?.body?.title} />
                                }             
                                {
                                    data?.id == PRODUCT.GIFT_CARD_API && content?.id == FEATURE.API_CLIENT && <CompanyLogo />
                                }
                                {
                                    data?.id == PRODUCT.GIFT_CARD_API && content?.id == FEATURE.BRAND_NETWORK && 
                                    <div className={styles['brand-network-container']}>
                                        <TitleNChips title={content?.body?.chipsNTitle?.title} chips={content?.body?.chipsNTitle?.chips} />
                                        <TitleNImage title={content?.body?.titleImage?.title} images={content?.body?.titleImage?.images} />
                                    </div>
                                } 
                                {
                                    data?.id == PRODUCT.GIFT_SHOP && content?.id == FEATURE.TECHNOLOGY && <div className={styles['titleNSlider-container']}>
                                        <TitleNSlider title={content?.body?.security?.title} slideData={content?.body?.security?.slideData} />
                                    </div>
                                }  
                                {
                                    data?.id == PRODUCT.GIFT_SHOP && content?.id == FEATURE.GIFT_SHOP_CLIENT && <CompanyLogo />
                                }
                                {
                                    data?.id == PRODUCT.GIFT_SHOP && content?.id == FEATURE.LOYALTY_POINTS && 
                                    <div>
                                        <PointEngine />
                                    </div>
                                    
                                }
                                {
                                    data?.id == PRODUCT.GIFT_SHOP && content?.id == FEATURE.USE_CASE && <>
                                        <TitleNChips title={content?.body?.chipsNTitle1?.title} chips={content?.body?.chipsNTitle1?.chips} />
                                        <Divider />
                                        <TitleNChips title={content?.body?.chipsNTitle2?.title} chips={content?.body?.chipsNTitle2?.chips} />
                                    </>
                                }
                                
                                {
                                    data?.id == PRODUCT.VALUE_ADDED_SERVICES && <ValueAddedUI conten={content} />
                                }
                                

                                
                            </section>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default CardInfo;