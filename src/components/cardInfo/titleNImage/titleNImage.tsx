import { useTranslation } from "next-i18next";
import styles from "./titleNImage.module.scss";
import { imageBaseUrl } from "@constants/envVariables";

const TitleNImage = ({ title, images }: any) => {
  const { t } = useTranslation();

  return (
    <div className={styles["title-image-container"]}>
      <h2>{t(title)}</h2>
      <div className={styles["image-wrapper"]}>
        {images?.map((item: any, index: number) => {
          return <img key={index} src={`${imageBaseUrl}/images/${item}`} />;
        })}
      </div>
    </div>
  );
};

export default TitleNImage;
