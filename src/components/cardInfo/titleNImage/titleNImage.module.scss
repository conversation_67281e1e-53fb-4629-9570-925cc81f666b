@import "@styles/variables";
@import "@styles/mixins";

.title-image-container {
  margin-top: 24px;

  @include rtl-styles {
    margin-top: 18px;
  }

  h2 {
    color: #b800c4;
    /* Body/B3 - Semi bold */
    font-family: "Mona Sans";
    font-size: var(--Font-Size-xs, 14px);
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 128.571% */
    letter-spacing: -0.14px;
    margin-bottom: 16px;

    @include rtl-styles {
      font-family: $arabic-font-family;
    }
  }
}

.image-wrapper {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-direction: row;
  overflow-x: scroll;

  &::-webkit-scrollbar {
    display: none;
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}
