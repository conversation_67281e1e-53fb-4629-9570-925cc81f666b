import { imageBaseUrl } from "@constants/envVariables";
import styles from './CardFeatureTile.module.scss'
import { useEffect } from "react";

const CardFeatureTile = ({className, title, callbackTileClick, id, icon}: any) => {
    const handleClick = () => {
        console.log('clicked tile', id);
        callbackTileClick(id);
    }

    useEffect(() => {
        console.log('icon', icon);
    },[icon]);
    return(
        <div className={`${styles['card-feature-tile']} ${className ? styles[className] : ''}`} onClick={()=>handleClick()}>
            <img src={icon} />
            <h2>{title}</h2>
        </div>
    )
}
export default CardFeatureTile;