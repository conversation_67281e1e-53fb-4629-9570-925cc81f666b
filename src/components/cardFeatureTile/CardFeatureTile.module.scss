@import "@styles/variables";
@import "@styles/mixins";

.card-feature-tile{
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
    width: 100%;
    max-width: 280px;
    border-radius: 8px;
    border: 0.5px solid #3c3c3c;
    background: rgba(255, 255, 255, 0.04);
    box-shadow: 0px 0px 47px 0px rgba(255, 255, 255, 0.05) inset;
    padding: 16px 0px 16px 16px;
    cursor: pointer;

    @include rtl-styles {
        padding: 16px 16px 16px 0px;
    }
    
    h2{
        color: #C2B8FF;
        /* Body/B4 - Seimi Bold */
        font-family: var(--font-mona-sans);
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        line-height: 18px; /* 133.333% */
        letter-spacing: -0.12px;

        @include rtl-styles {
            font-family: $arabic-font-family;
          }
    }
}

.active{
    border: 1px solid #B800C4;
    h2{
        color: #B800C4;
    }
}