"use client";

import React, { useRef, useState } from "react";
import styles from "./ContactUs.module.scss";
import {
  TextField,
  Button,
  InputAdornment,
  CircularProgress,
} from "@mui/material";
import { useTranslation } from "next-i18next";
import { imageBaseUrl } from "@constants/envVariables";
import isNumber from "utils/isNumber";
import { isValidEmailAddress } from "utils/emailValidation";
import ReCAPTCHA from "react-google-recaptcha";
import { CAPTCHA_SITE_KEY } from "@constants/envVariables";
import { API_ENDPOINTS } from "@constants/common";
import { useNotifier } from "components/common/notifier/NotifierContext";
import isValidPhoneNumber from "utils/isValidPhoneNumber";
import CountryPhoneCode from "components/common/countryPhoneCode/CountryPhoneCode";

interface ContactUsProps {
  onClose?: () => void;
}

const ContactUs: React.FC<ContactUsProps> = ({ onClose }) => {
  const { t, i18n } = useTranslation();

  const locale = i18n.language;

  const reCaptchaRef = useRef<any>(null);

  // #. error messages
  const invalidPhoneNumberMsg = t("invalidPhoneNumber");
  const nameRequired = t("nameRequired");
  const invalidEmailMessage = t("invalidEmailAddress");
  const basePath = "/solutions-hub";

  const [phoneNumber, setPhoneNumber] = useState<any>("");
  const [phoneNumberVal, setPhoneNumberVal] = useState<any>("");
  const [dialCodeVal, setDialCodeVal] = useState<any>("+971");
  const [countryCode, setCountryCode] = useState<any>("AE");
  const [validatePhoneNumber, setValidatePhoneNumber] = useState("");
  const [nameValidate, setNameValidate] = useState("");
  const [name, setName] = useState("");
  const [message, setMessage] = useState("");
  const [email, setEmail] = useState("");
  const [invalidEmail, setInvalidEmail] = useState("");
  const [notifierData, setNotifierData] = useState({});
  const [loading, setLoading] = useState(false);
  const [jobTitle, setJobTitle] = useState("");
  const [jobTitleValidate, setJobTitleValidate] = useState("");
  const [companyName, setCompanyName] = useState("");
  const [companyNameValidate, setCompanyNameValidate] = useState("");

  const phoneNumberRef = useRef<HTMLInputElement>(null!);

  const { showNotification } = useNotifier();

  // Onchange method for name, message, email and phone field
  const handleChange = (event: any, type: string) => {
    const { value } = event?.target;
    if (type === "name") {
      setName(String(event?.target?.value).trimStart());
    } else if (type === "phone") {
      const inputNumber = event?.target?.value;
      setPhoneNumber(event.target.value);
      setPhoneNumberVal(`${dialCodeVal}${event.target.value}`);
      const isValid = inputNumber
        ? isValidPhoneNumber(event.target.value, dialCodeVal)
        : true;
      let msg = "";
      if (!isValid) {
        msg = invalidPhoneNumberMsg;
      }
      setValidatePhoneNumber(msg);
    } else if (type === "email") {
      const email = String(event?.target?.value).trimStart();
      setEmail(email);
      const trimmedEmail = email?.replace(/\s+$/, "");
    } else if (type === "message") {
      setMessage(String(event?.target?.value).trimStart());
    } else if (type === "jobTitle") {
      if (value.length > 0) {
        setJobTitleValidate("");
      }
      setJobTitle(String(event?.target?.value).trimStart());
    } else if (type === "companyName") {
      if (value.length > 0) {
        setCompanyNameValidate("");
      }
      setCompanyName(String(event?.target?.value).trimStart());
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    let isValid = true;

    // Validate name
    if (!name || name.length < 2) {
      setNameValidate(nameRequired);
      isValid = false;
    } else {
      setNameValidate("");
    }

    // Validate email
    if (!email || !isValidEmailAddress(email) || email.length > 250) {
      setInvalidEmail(invalidEmailMessage);
      isValid = false;
    } else {
      setInvalidEmail("");
    }

    // Validate Job Title
    if (!jobTitle) {
      setJobTitleValidate(t("jobTitleRequired"));
      isValid = false;
    } else {
      setJobTitleValidate("");
    }

    // Validate company name
    if (!companyName) {
      setCompanyNameValidate(t("companyNameRequired"));
      isValid = false;
    } else {
      setCompanyNameValidate("");
    }

    if (phoneNumber) {
      const valid = isValidPhoneNumber(phoneNumber, dialCodeVal);

      if (!valid) {
        setValidatePhoneNumber(invalidPhoneNumberMsg);
      }
      isValid = valid;
    } else {
      setValidatePhoneNumber("");
    }

    if (isValid) {
      initiateCaptchaValidation();
      setLoading(true);
    }
  };

  // Handler for captcha error
  const handleCaptchaError = () => {
    console.error("Captcha error occurred");
  };

  // Sends the inquiry form data to the server
  const submitInquiry = async (token: string | null) => {
    if (!token) {
      console.error("Missing captcha token");
      return;
    }

    try {
      const response = await fetch(`${basePath}${API_ENDPOINTS.SEND_INQUIRY}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          token,
          message,
          name,
          email,
          phoneNumberVal,
          jobTitle,
          companyName,
        }),
      });

      const result = await response.json();

      if (result?.status_code === 200) {
        setLoading(false);
        showNotification({
          title: "",
          description: t("inquirySubmittedSuccessfully"),
          icon: "checkOutline",
        });
      } else {
        setLoading(false);
        showNotification({
          title: "",
          description: t("somethingWrong"),
          icon: "errorOutline",
        });
      }

      if (onClose) {
        onClose();
      }
    } catch (error) {
      console.error("Error verifying captcha:", error);
    }
  };

  // Triggered when captcha changes
  const handleCaptchaChange = async (token: string | null) => {
    await submitInquiry(token);
  };

  // Programmatically trigger captcha validation
  const initiateCaptchaValidation = async () => {
    try {
      if (reCaptchaRef.current) {
        reCaptchaRef.current.reset();
        await reCaptchaRef.current.executeAsync();
      }
    } catch (error) {
      console.error("Error executing captcha:", error);
    }
  };

  /**
   * @method onPhonecodeChanged
   * @param phoneCode
   */
  const onPhonecodeChanged = ({ dialCode, countryCode }: any) => {
    setPhoneNumberVal("");
    setPhoneNumber("");
    setValidatePhoneNumber("");
    setCountryCode(countryCode);
    setDialCodeVal(dialCode);
  };

  /**
   * @method onValidateName
   * @param event
   */
  const onValidateName = (event: any) => {
    const name = String(event?.target?.value).trimStart();
    if (name && name.length >= 2) {
      setNameValidate("");
    } else {
      setNameValidate(nameRequired);
    }
  };

  /**
   * @method onValidateEmail
   * @param event
   */
  const onValidateEmail = (event: any) => {
    const email = String(event?.target?.value).trimStart();
    const message =
      isValidEmailAddress(email) && email.length <= 250
        ? ""
        : invalidEmailMessage;
    setInvalidEmail(event?.target?.value?.length > 0 ? message : "");
  };

  console.log(phoneNumberVal, "phoneNumberVal");

  return (
    <div className={`${styles["formContainer"]} formContainer`}>
      <span className={styles["closeIcon"]} onClick={onClose}>
        <img
          src={`${imageBaseUrl}/images/close-circle.svg`}
          alt="close-icon"
          height={32}
          width={32}
        />
      </span>

      <h2 className={styles["heading"]}>{t("howHelp")}</h2>

      <div className={styles["specialistInfo"]}>
        <div className={styles["specialistAvatar"]}>
          <img
            src={`${imageBaseUrl}/images/profile-pic.svg`}
            alt="Gift Card Specialist"
          />
        </div>
        <div className={styles["specialistText"]}>
          <p>{t("cardSpecilist")}</p>
          <p>{t("assistYou")}</p>
        </div>
      </div>

      <div className={styles["inquirySection"]}>
        <h5 className={styles["inquiryHeading"]}>{t("lookingFor")}</h5>

        <TextField
          name="message"
          placeholder={t("placeHolder")}
          variant="outlined"
          fullWidth
          multiline
          rows={3}
          value={message}
          onChange={(event) => {
            handleChange(event, "message");
          }}
          className={styles["messageField"]}
        />
      </div>

      <form onSubmit={handleSubmit} className={styles["form"]}>
        <div className={styles["formRow"]}>
          <TextField
            name="name"
            placeholder={t("yourName")}
            variant="outlined"
            fullWidth
            value={name}
            onChange={(event) => {
              handleChange(event, "name");
              onValidateName(event);
            }}
            onBlur={(event) => {
              onValidateName(event);
            }}
            className={styles["textField"]}
            inputProps={{
              maxLength: 26,
            }}
            helperText={nameValidate}
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <span className={styles["inputIcon"]}>
                      <img
                        src={`${imageBaseUrl}/images/profile-hub.svg`}
                        alt="User Icon"
                        height={24}
                        width={24}
                      />
                    </span>
                  </InputAdornment>
                ),
              },
            }}
          />
        </div>

        <div className={styles["formRow"]}>
          <TextField
            name="email"
            placeholder={t("yourBusiness")}
            variant="outlined"
            fullWidth
            value={email}
            onChange={(event) => {
              handleChange(event, "email");
              onValidateEmail(event);
            }}
            onBlur={onValidateEmail}
            helperText={invalidEmail}
            className={styles["textField"]}
            type="email"
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <span className={styles["inputIcon"]}>
                      <img
                        src={`${imageBaseUrl}/images/sms-hub.svg`}
                        alt="User Icon"
                        height={24}
                        width={24}
                      />
                    </span>
                  </InputAdornment>
                ),
              },
            }}
          />
        </div>

        <div className={styles["formRow"]}>
          <TextField
            name="mobile"
            type="text"
            placeholder={t("mobileOptional")}
            variant="outlined"
            inputRef={phoneNumberRef}
            fullWidth
            value={phoneNumber}
            helperText={validatePhoneNumber}
            onChange={(event) => {
              handleChange(event, "phone");
            }}
            className={`${styles["textField"]}`}
            inputProps={{ maxLength: 12 }}
            onPaste={(event: any) => {
              if (event.clipboardData.getData("Text").match(/[^\d]/)) {
                event.preventDefault();
              }
            }}
            onKeyDown={(event: any) => {
              if (!isNumber(event)) {
                event.preventDefault();
              }
            }}
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start" sx={{ marginRight: 0 }}>
                    <span className={styles["inputIcon"]}>
                      <img
                        src={`${imageBaseUrl}/images/chat-hub.svg`}
                        alt="User Icon"
                        height={24}
                        width={24}
                      />
                    </span>
                    <CountryPhoneCode
                      setCountryCallback={onPhonecodeChanged}
                      showCountryFlag={false}
                      showCountryName={true}
                      showCountryNameInSelection={false}
                      enableSearch={true}
                      defaultValue={countryCode}
                      dialCode={dialCodeVal}
                      phoneNumberRef={phoneNumberRef}
                      language={"en"}
                    />
                  </InputAdornment>
                ),
              },
            }}
          />
        </div>

        <div className={styles["formRow"]}>
          <TextField
            name="jobTitle"
            placeholder={t("jobTitle")}
            variant="outlined"
            fullWidth
            value={jobTitle}
            onChange={(event) => {
              handleChange(event, "jobTitle");
            }}
            helperText={jobTitleValidate}
            className={styles["textField"]}
            type="text"
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <span className={styles["inputIcon"]}>
                      <img
                        src={`${imageBaseUrl}/images/tag-user.svg`}
                        alt="User"
                        height={24}
                        width={24}
                      />
                    </span>
                  </InputAdornment>
                ),
              },
            }}
          />
        </div>

        <div className={styles["formRow"]}>
          <TextField
            name="companyName"
            placeholder={t("companyName")}
            variant="outlined"
            fullWidth
            value={companyName}
            onChange={(event) => {
              handleChange(event, "companyName");
            }}
            helperText={companyNameValidate}
            className={styles["textField"]}
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <span className={styles["inputIcon"]}>
                      <img
                        src={`${imageBaseUrl}/images/company.svg`}
                        alt="Company"
                        height={24}
                        width={24}
                      />
                    </span>
                  </InputAdornment>
                ),
              },
            }}
          />
        </div>

        <div className={styles["formActions"]}>
          <Button
            type="submit"
            variant="contained"
            className={styles["sendButton"]}
            // disabled={isSubmitting}
          >
            {loading ? <CircularProgress /> : t("inquiry")}
          </Button>
        </div>
      </form>

      <ReCAPTCHA
        id="id1"
        theme="light"
        size="invisible"
        hl={locale}
        ref={reCaptchaRef}
        badge="bottomright"
        sitekey={CAPTCHA_SITE_KEY || ""}
        onErrored={handleCaptchaError}
        onChange={handleCaptchaChange}
      />
    </div>
  );
};

export default ContactUs;
