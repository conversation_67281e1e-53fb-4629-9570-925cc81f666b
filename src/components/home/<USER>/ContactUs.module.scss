@import "@styles/variables";
@import "@styles/mixins";

.formContainer {
  width: 483px;
  padding: 40px;
  border-radius: 24px;
  border: 2px solid $barney-purple;
  background: $lavender;
  position: relative;
  z-index: 1000;
}

.closeIcon {
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: pointer;

  @include rtl-styles {
    right: auto;
    left: 10px;
  }
}

.heading {
  font-family: $bricolage-font-family;
  color: $jet-black;
  font-size: 40px;
  font-style: normal;
  font-weight: 800;
  line-height: 42px;
  letter-spacing: -1px;

  @include rtl-styles {
    font-family: $arabic-font-family;
  }
}

.specialistInfo {
  display: flex;
  align-items: center;
  gap: 24px;
  margin: 24px 0 30px 0;

  .specialistAvatar {
    width: 76px;
    height: 76px;
    aspect-ratio: 1/1;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid white;

    img {
      display: block;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .specialistText {
    p {
      font-family: $default-font-family;
      color: rgba(15, 15, 15, 0.8);
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 25.2px; /* 157.5% */
    }
  }
}

.inquirySection {
  margin-bottom: 30px;
  border-radius: 16px;
  border: 1px solid $jet-black;
  padding: 16px;

  .inquiryHeading {
    font-family: $bricolage-font-family;
    color: $jet-black;
    font-size: 18px;
    font-style: normal;
    font-weight: 800;
    line-height: 120%;
    margin-bottom: 16px;

    @include rtl-styles {
      font-family: $arabic-font-family;
    }
  }

  .messageField {
    :global(.MuiOutlinedInput-root) {
      border: none;
      outline: none;
      padding: 0;
    }

    :global(.MuiInputBase-input) {
      color: $jet-black;
      font-family: $default-font-family;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 140%;

      &::placeholder {
        color: $jet-black !important;
        opacity: 1;
      }
    }

    fieldset {
      border: none;
    }
  }
}

.form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.formRow {
  width: 100%;
}

.textField {
  width: 100%;

  input.last-child {
    padding-right: 30px;
  }

  fieldset {
    border: none;
  }

  :global(.MuiOutlinedInput-root) {
    border-radius: 8px;
    padding: 16px;
    border-radius: 16px;
    border: 1px solid $jet-black;
    background: $lavender;

    &:hover .MuiOutlinedInput-notchedOutline {
      border-color: $barney-purple;
    }

    &.Mui-focused .MuiOutlinedInput-notchedOutline {
      border-color: $barney-purple;
      border-width: 2px;
    }
  }

  :global(.MuiInputAdornment-root) {
    @include rtl-styles {
      margin-left: 8px !important;
      margin-right: 0 !important;
    }
  }

  :global(.MuiInputBase-input) {
    color: $jet-black;
    font-family: $default-font-family;
    padding: 0;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 140%;

    &::placeholder {
      color: rgba(15, 15, 15, 0.5) !important;
      opacity: 1;
    }
  }
}

.inputIcon {
  width: 24px;
  height: 24px;
}

.formActions {
  display: flex;
  margin-top: 16px;
}

.sendButton {
  font-family: $default-font-family !important;
  font-size: 16px !important;
  font-style: normal !important;
  font-weight: 600 !important;
  line-height: 20px !important;
  padding: 14px 32px !important;
  color: $lavender !important;
  border-radius: 48px !important;
  border: 1px solid #b800c4 !important;
  background: linear-gradient(0deg, #0f0f0f 0%, #0f0f0f 100%), #0f0f0f !important;
  text-transform: initial !important;
  width: 170px;

  @include rtl-styles {
    font-family: $arabic-font-family !important;
  }

  &:hover {
  }

  &:disabled {
    background-color: rgba(0, 0, 0, 0.5) !important;
  }
  
  span {
    width: 20px !important;
    height: 20px !important;
    svg {
      width: 20px !important;
    }
  }
}

.country-code {
  color: #0f0f0f;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  padding: 0 8px;
  unicode-bidi: plaintext;
}
