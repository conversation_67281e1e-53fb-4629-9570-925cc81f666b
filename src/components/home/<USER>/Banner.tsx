"use client";

import React from "react";
import styles from "./Banner.module.scss";
import { imageBaseUrl } from "@constants/envVariables";
import RotatingGlobe from "components/common/rotatingGlobe/RotatingGlobe";

const Banner = () => {
  const backgroundImage = `${imageBaseUrl}/images/solutionHubBg.png`;

  return (
    <>
      <style jsx>{`
        .banner {
          background: url("${backgroundImage}");
          background-repeat: no-repeat;
          background-size: cover;
        }
      `}</style>
      <div className={`container ${styles["banner"]}`}>
        <RotatingGlobe />
      </div>
    </>
  );
};

export default Banner;
