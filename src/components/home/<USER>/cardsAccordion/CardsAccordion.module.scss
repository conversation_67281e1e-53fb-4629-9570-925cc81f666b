@import "@styles/variables";
@import "@styles/mixins";

.cards-accordion {
  display: flex;
  flex-direction: column;
  position: relative;
  margin-bottom: -150px;

  .card-item {
    border-radius: 24px;
    padding: 32px 32px 72px;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease-in-out;


    &:nth-child(1) {
      top: 0px;
    }

    &:nth-child(2) {
      top: -38px;
    }

    &:nth-child(3) {
      top: -76px;
    }

    &:nth-child(4) {
      top: -114px;
    }

    &:nth-child(5) {
      top: -152px;
    }
  }

  .marquee-wrapper {
    display: flex;
    justify-content: space-between;
  }

  .card-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    unicode-bidi: plaintext;
    font-family: $bricolage-font-family;
    font-optical-sizing: none;
    font-size: 24px;
    font-weight: 800;
    line-height: 34px;

    &__link {
      display: flex;
      height: 34px;
      padding: 8px 16px;
      justify-content: center;
      align-items: center;
      gap: 2px;
      border-radius: 45px;
      border: 1px solid #C2B8FF;
      background: #1B0A1F;
      color: #C2B8FF;
      font-size: 14px;
      font-weight: 600;
      line-height: 18px;
      letter-spacing: -0.14px;
      width: 167px;

      @include rtl-styles {
        font-family: $arabic-font-family;
        width: 174px;
        margin-right: 12px;
      }

      .arrow-right {
        @include rtl-styles {
          transform: rotateY(180deg);
        }
      }

      &-go {
        @include rtl-styles {
          width: 270px;
        }
      }
    }

    .arrow-down {
      margin-inline-start: 14px;

      @include rtl-styles {
        transform: rotateY(180deg);
      }
    }

    @include rtl-styles {
      font-family: $arabic-font-family;
    }

    &__detail {
      display: flex;
      height: 34px;
      padding: 8px 16px;
      justify-content: center;
      align-items: center;
      gap: 2px;
      border-radius: 100px;
      border: 1px solid #B800C4;
      background: #1B0A1F;
      margin-inline-start: 14px;

      .arrow-right {
        @include rtl-styles {
          transform: rotateY(180deg);
        }
      }
    }
  }

  .border {
    height: 1px;
    background-color: rgba(194, 184, 255, 0.20);
    margin-block: 24px;
  }

  .card-content {

    .card-desc {
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 130%;
      margin: 0;


      @include rtl-styles {
        font-family: $arabic-font-family;
      }
    }

    .tags {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-top: 40px;
      cursor: pointer;
      flex-wrap: wrap;

      @include rtl-styles {
        font-family: $arabic-font-family;
      }

      &::-webkit-scrollbar {
        display: none;
        -ms-overflow-style: none;
        scrollbar-width: none;
      }

      &__tag {
        padding: 4px 8px;
        display: flex;
        align-items: center;
        height: 24px;
        border-radius: 16px;
        font-size: 10px;
        font-style: normal;
        font-weight: 500;
        line-height: 140%;
        letter-spacing: 0.4px;
        text-transform: uppercase;
        min-width: fit-content;
        overflow: hidden;
      }
    }
  }

  .card-content-wrapper {
    overflow: hidden;
    max-height: 0;
    opacity: 0;
    transform: translateY(-15px);
    transition: max-height 0.8s cubic-bezier(0.4, 0, 0.2, 1),
      opacity 0.8s ease,
      transform 0.5s ease;

    &.open {
      max-height: 260px;
      opacity: 1;
      transform: translateY(0);
      transition: max-height 0.8s cubic-bezier(0.4, 0, 0.2, 1),
        opacity 0.8s ease,
        transform 0.5s ease;
    }

    &:not(.open) {
      transition: none;
    }
  }
}

.ignore-elem {
  display: contents;
}