"use client";
import React, { useEffect, useState } from "react";
import styles from "./CardsAccordion.module.scss";
import { useTranslation } from "react-i18next";
import { usePathname, useRouter } from "next/navigation";
import { atWorkUrl, ECOM_BASE_URL, imageBaseUrl } from "@constants/envVariables";
import { CARD_TITLE } from "@constants/common";
import { Box, Modal } from "@mui/material";
import ContactUs from "components/home/<USER>/ContactUs";
import TransitionLink from "features/common/transitionLink/transitionLink";
import Marquee from "react-fast-marquee";
import Image from "next/image";

interface Card {
  id: number;
  title: string;
  description: string;
  tags: string[];
  borderColor: string;
  backgroundColor: string;
  index: number;
  color?: string;
  slug?: string;
  tagsBg: string;
  goToLink?: boolean;
}

interface CardsAccordionProps {
  cardsData: Card[];
  type: string;
  isDetailPage?: boolean;
}

const CardsAccordion = ({ cardsData, type, isDetailPage }: CardsAccordionProps) => {
  const {
    t,
    i18n: { language }
  } = useTranslation();
  const pathname = usePathname();
  const router = useRouter();

  const [openModal, setOpenModal] = useState(false);
  const solutionHubArrow = `${imageBaseUrl}/images/arrow-right.svg`;
  const arrowDown = `${imageBaseUrl}/images/arrow-down.svg`;

  const [activeIndex, setActiveIndex] = useState(cardsData[0]?.id);

  const handleOpenModal = () => {
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
  };

  const handleCardClick = (card: Card) => {
    if (card?.title === CARD_TITLE.JOIN_BRANDS) {
      handleOpenModal();
      return;
    }
    if(isDetailPage){
      router.push(`/${pathname.split("/")[1]}/${card?.slug}`)
      return;
    }
    const targetDiv = document?.getElementById("active-card");
    targetDiv?.scrollIntoView({
      behavior: "smooth",
      block: "end"
    });
    setActiveIndex(card?.id);
  };

  const handleGoToLink=(e: React.MouseEvent, card: Card)=>{
    e.preventDefault()
    let url;
    if(card?.title === CARD_TITLE.AT_WORK){
      url = atWorkUrl;
    } else if(card?.title === CARD_TITLE.GIFTING_WALLET){
      url = ECOM_BASE_URL
    }
    window.open(url, "_blank")
  }

    useEffect(() => {
      if(isDetailPage){
      const urlSlug = pathname.split("/")[2];
      const matchingCardIndex = cardsData.findIndex((card) => card.slug === urlSlug);
      setActiveIndex(matchingCardIndex + 1)
      }
    }, [pathname, cardsData]);

  return (
    <>
      <div className={styles["cards-accordion"]} id='active-card'>
        {cardsData.map((card, index) => {
          const isActive = activeIndex === card.id;

          return (
            <div
              key={card.id}
              className={`${styles["card-item"]} ${
                isActive
                  ? styles["card-item__active"]
                  : styles["card-item__inactive"]
              }`}
              style={{
                backgroundColor: card?.backgroundColor,
                border: `3px solid ${card.borderColor}`,
                zIndex: card?.id
              }}
              onClick={() => handleCardClick(card)}
            >
              {isActive ? (
                <div className={styles["marquee-wrapper"]}>
                  <Marquee direction='right' play={t(card?.title)?.length > 35}>
                    <TransitionLink
                      href={`/${pathname.split("/")[1]}/${card.slug}`}
                      className={styles["card-title"]}
                      // @ts-ignore
                      style={{
                        color: card?.color ? `${card?.color}` : card.borderColor
                      }}
                    >
                       {card?.title === CARD_TITLE.AT_WORK
                    ? `@${t(card.title)}`
                    : t(card.title)}
                    </TransitionLink>
                  </Marquee>
                  {card?.goToLink && !isDetailPage ? (
                    <a className={`${styles["card-title__link"]} ${styles["card-title__link-go"]}`} onClick={(e) => handleGoToLink(e, card)}
                    style={{
                      backgroundColor: card?.backgroundColor,
                      borderColor: card?.borderColor,
                      color: card?.borderColor
                    }}
                    >
                      {t("goToPage")}
                      <Image
                        className={styles["arrow-right"]}
                        src={solutionHubArrow}
                        height={16}
                        width={16}
                        alt='solution-hub'
                      />
                    </a>
                  ) : (
                    <TransitionLink
                      href={`/${pathname.split("/")[1]}/${card.slug}`}
                      className={isDetailPage ? styles["card-title__detail"] : styles["card-title__link"]}
                    >
                    {isDetailPage ? <Image
                        className={styles["arrow-right"]}
                        src={solutionHubArrow}
                        height={16}
                        width={16}
                        alt='solution-hub'
                      /> :  
                    <>
                    {t("learnMore")}
                      <Image
                        className={styles["arrow-right"]}
                        src={solutionHubArrow}
                        height={16}
                        width={16}
                        alt='solution-hub'
                      />
                      </>}
                    </TransitionLink>
                  )}
                </div>
              ) : (
                <a
                  style={{
                    color: card?.color ? `${card?.color}CC` : card.borderColor
                  }}
                  className={styles["card-title"]}
                  href={`/${pathname.split("/")[1]}/${card.slug}`}
                  onClick={(e) => e.preventDefault()}
                >
                  {card?.title === "atWork"
                    ? `@${t(card.title)}`
                    : t(card.title)}
                  <Image
                    className={styles["arrow-down"]}
                    src={
                      card?.title === CARD_TITLE.JOIN_BRANDS
                        ? solutionHubArrow
                        : arrowDown
                    }
                    height={16}
                    width={16}
                    alt='arrow-down'
                  />
                </a>
              )}

              <div
                className={`${styles["card-content-wrapper"]} ${
                  isActive ? styles["open"] : ""
                }`}
              >
                <div className={styles["border"]} />
                <div className={`${styles["card-content"]}`}>
                  <p
                    className={styles["card-desc"]}
                    style={{
                      color: card.color ? card.color : card.borderColor
                    }}
                  >
                    {t(card.description)}
                  </p>
                  <div className={styles["tags"]}>
                    {card.tags.map((tag, index) => (
                      <TransitionLink
                        href={`/${pathname.split("/")[1]}/${card.slug}/${tag}`}
                        key={index}
                        className={`${styles["ignore-elem"]} ${styles["tags__tag"]}`}
                        //   @ts-ignore
                        style={{
                          color: `${
                            card?.color ? card?.color : card?.borderColor
                          }CC`,
                          background: card?.tagsBg,
                          cursor: "pointer"
                        }}
                      >
                        {t(tag)}
                      </TransitionLink>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Contact Us Modal */}
      <Modal
        open={openModal}
        disableEscapeKeyDown
        disableAutoFocus
        disableEnforceFocus
        disableRestoreFocus
        disablePortal={false}
        hideBackdrop={false}
        disableScrollLock
        onClose={handleCloseModal}
        aria-labelledby='contact-us-modal'
        aria-describedby='contact-us-form-modal'
        slotProps={{
          backdrop: {
            className: "modal__backdrop"
          }
        }}
      >
        <Box className='modal__content'>
          <ContactUs onClose={handleCloseModal} />
        </Box>
      </Modal>
    </>
  );
};

export default CardsAccordion;
