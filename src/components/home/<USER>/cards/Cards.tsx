"use client";

import React, { useState, useEffect } from "react";
import styles from "./Cards.module.scss";
import Image from "next/image";
import { useTranslation } from "next-i18next";
import { useRouter, usePathname } from "next/navigation";
import { CARD_TITLE } from "@constants/common";
import TransitionLink from "features/common/transitionLink/transitionLink";
import { imageBaseUrl } from "@constants/envVariables";
import { Box, Modal } from "@mui/material";
import ContactUs from "components/home/<USER>/ContactUs";
import Marquee from "react-fast-marquee";

export interface Card {
  id: number;
  title: string;
  description: string;
  tags: string[];
  borderColor: string;
  backgroundColor: string;
  index: number;
  color?: string;
  slug?: string;
  tagsBg: string;
}

interface CardsProps {
  cardsData: Card[];
  type: string;
  page?: string;
  isDetailPage?: boolean;
}

const Cards = ({ cardsData, type, page, isDetailPage }: CardsProps) => {
  const { t,i18n: {language} } = useTranslation();
  const [cards, setCards] = useState<Card[]>(cardsData);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [openModal, setOpenModal] = useState(false);

  const router = useRouter();
  const isRTL = language === "ar";

  const solutionHubArrow =
    page === "home" ? "solution-hub.svg" : "solution-hub-active.svg";

  const pathname = usePathname();

  const handleOpenModal = () => {
    setOpenModal(true);
  };

  useEffect(() => {
    console.log("pahtname", pathname);
  }, []);

  useEffect(() => {
    const urlSlug = pathname.split("/")[2];
    const matchingCardIndex = cardsData.findIndex((card) => card.slug === urlSlug);

    if (matchingCardIndex !== -1) {
      const activeCard = cardsData[matchingCardIndex];
      const rotatingCards = cardsData.filter((card) => card.title !== CARD_TITLE.JOIN_BRANDS);
  
      const cardsAfterActive = rotatingCards.slice(matchingCardIndex + 1);
      const cardsBeforeActive = rotatingCards.slice(0, matchingCardIndex);
  
      // Maintain order for all except "jointBrand"
      const reorderedCards = [
        activeCard,
        ...cardsAfterActive,
        ...cardsBeforeActive,
      ].map((card, index) => ({
        ...card,
        index: card.title === CARD_TITLE.JOIN_BRANDS ? 0 : 4 - index,
      }));
      
      // Append "jointBrand" as the last item
      const jointBrandCard = cardsData.find((card) => card.title === CARD_TITLE.JOIN_BRANDS);
      if (jointBrandCard) {
        reorderedCards.push({ ...jointBrandCard, index: 0 });
      }
  
      setCards(reorderedCards);
    }
  }, [pathname, cardsData]);

  // Function to rotate cards every 5 seconds
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (!isHovered && page === "home") {
      interval = setInterval(() => {
        setIsAnimating(true);
        setCards((prevCards) => {
          const staticCard = prevCards.find((card) => card.title === CARD_TITLE.JOIN_BRANDS);
          const rotatingCards = prevCards.filter((card) => card.title !== CARD_TITLE.JOIN_BRANDS);
  
          if (rotatingCards.length > 1) {
            const activeCard = rotatingCards.shift();
            if (activeCard) {
              rotatingCards.push(activeCard);
            }
          }
  
          const updatedCards = [...rotatingCards, staticCard].filter(Boolean);
  
          return updatedCards.map((card, index) => ({
            ...card,
            index: card?.title === CARD_TITLE.JOIN_BRANDS ? 1 : 5 - index, // Keep the static card always last
          }))as Card[];
        });
        setTimeout(() => {
          setIsAnimating(false);
        }, 100);
      }, 5000);
    }

    return () => clearInterval(interval);
  }, [cards.length, isHovered, page]);

  const handleCardClick = (selectedCard: any) => {
    const targetDiv = document?.getElementById("active-card");
    targetDiv?.scrollIntoView({
      behavior: "smooth",
      block: "end",
    })

    if(selectedCard?.title === CARD_TITLE.JOIN_BRANDS){
      handleOpenModal()
      return
    }

    if(isDetailPage){
      router.push(`/${pathname.split("/")[1]}/${selectedCard?.slug}`)
    }
    setCards((prevCards) => {
      const staticCard = prevCards.find((card) => card.title === CARD_TITLE.JOIN_BRANDS);
      const rotatingCards = prevCards.filter((card) => card.title !== CARD_TITLE.JOIN_BRANDS);
  
      const clickedCardIndex = rotatingCards.findIndex((card) => card.id === selectedCard?.id);
      if (clickedCardIndex === -1) return prevCards;

      // Get the clicked card and reorder the rest
      const clickedCard = rotatingCards[clickedCardIndex];
      const cardsAfterClicked = rotatingCards.slice(clickedCardIndex + 1);
      const cardsBeforeClicked = rotatingCards.slice(0, clickedCardIndex);
      const newCards = [clickedCard, ...cardsAfterClicked, ...cardsBeforeClicked];
  
      // Ensure staticCard is valid before appending
      const updatedCards = staticCard ? [...newCards, staticCard] : newCards;
  
      return updatedCards.map((card, index) => ({
        ...card,
        index: card?.title === CARD_TITLE.JOIN_BRANDS ? 0 : 4 - index,
        id: card.id ?? `unique-${index}`,
      }));
    });
  };

  const activeCard = cards[0];
  const inactiveCards = cards.slice(1);

  const handleRedirection = (tag: string) => {
    router.push(`/${pathname.split("/")[1]}/${activeCard.slug}/${tag}`);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
  };
  return (
    <div
      className={` ${styles["cards-container"]} ${
        type === "brands" && styles["brands"]
      }`}
    >
      <div
        id="active-card"
        key={activeCard.id}
        className={`${styles["active-card"]} ${
          page !== "home" && styles["active-card-home"]
        }`}
        style={{
          background: activeCard?.backgroundColor,
          backgroundColor: activeCard.backgroundColor,
          border: `3px solid ${activeCard.borderColor}`,
          zIndex: 100,
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <TransitionLink href={`/${pathname.split("/")[1]}/${activeCard.slug}`}>
          <div className={styles["active-card__heading"]}>
            <div
              style={{
                color: activeCard?.color
                  ? activeCard?.color
                  : activeCard.borderColor,
              }}
            >
              {activeCard?.title === "atWork" ? (
                <>
                  <span>@</span>
                  {t(activeCard.title)}
                </>
              ) :
               t(activeCard?.title)?.length > 35 && isRTL ? (
                <Marquee
                direction={isRTL ? "right" : "left"}
                >{t(activeCard.title)} 
                <div style={{marginInlineStart:'20px'}} /> </Marquee>
              ) : (
                t(activeCard.title)
              )
              }
            </div>
            <Image
              src={`${imageBaseUrl}/images/${solutionHubArrow}`}
              height={32}
              width={32}
              alt="solution-hub"
            />
          </div>
        </TransitionLink>

        <p
          className={styles["active-card__description"]}
          style={{
            color: activeCard.color ? activeCard.color : activeCard.borderColor,
          }}
        >
          {t(activeCard.description)}
        </p>

        <div className={styles["active-card__tag-section"]}>
          {activeCard?.tags?.map((tag, index) => (
            <TransitionLink
              href={`/${pathname.split("/")[1]}/${activeCard.slug}/${tag}`}
              key={index}
              className={styles["ignore-elem"]}
            >
              <span
                key={index}
                className={styles["active-card__tag-section__tag"]}
                style={{
                  color: `${
                    activeCard?.color
                      ? activeCard?.color
                      : activeCard?.borderColor
                  }CC`,
                  background: activeCard?.tagsBg,
                  cursor: "pointer",
                }}
              >
                {t(tag)}
              </span>
            </TransitionLink>
          ))}
        </div>
      </div>

      {inactiveCards.map((card, index) => (
        <div
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          key={`card-${card.id}-${isAnimating ? "animating" : "static"}`}
          className={`${styles["inactive-card"]} ${
            isAnimating ? styles["animating"] : ""
          } ${page !== "home" && styles["active-card-home"]}`}
          style={{
            background: card?.backgroundColor,
            backgroundColor: card?.backgroundColor,
            border: `2px solid ${card.borderColor}`,
            zIndex: card.index,
            bottom:
              index === 0
                ? "31%"
                : index === 1
                ? "16%"
                : index === 2
                ? "0%"
                : "-15%",
          }}
          onClick={() => handleCardClick(card)}
        >
          <a style={{
                color: activeCard?.color
                  ? `${activeCard?.color}CC`
                  : card.borderColor,
              }} href={`/${card?.slug}`} onClick={(e) => e.preventDefault()}>
              {card?.title === "atWork" ? (
                  `@${t(card.title)}`
              ) : (
                t(card.title)
              )}
          </a>
        </div>
      ))}

      {/* Contact Us Modal */}
      <Modal
        open={openModal}
        disableEscapeKeyDown
        disableAutoFocus
        disableEnforceFocus
        disableRestoreFocus
        disablePortal={false}
        hideBackdrop={false}
        disableScrollLock
        onClose={handleCloseModal}
        aria-labelledby="contact-us-modal"
        aria-describedby="contact-us-form-modal"
        slotProps={{
          backdrop: {
            className: "modal__backdrop",
          },
        }}
      >
        <Box className="modal__content">
          <ContactUs onClose={handleCloseModal} />
        </Box>
      </Modal>
    </div>
  );
};

export default Cards;
