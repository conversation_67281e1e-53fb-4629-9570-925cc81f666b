@import "@styles/variables";
@import "@styles/mixins";

@keyframes slideDown {
  0% {
    transform: translateY(-20px) scale(1);
    // opacity: 0;
    // z-index: 1;
  }
  100% {
    transform: translateY(0) scale(1);
    // opacity: 1;
    // z-index: 2;
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0.9;
  }
  100% {
    opacity: 1;
  }
}

.active-card-home {
  width: 100% !important;
}

.active-card {
  padding: 24px 24px 40px 24px;
  border-radius: 24px;
  height: 260px;
  position: relative;
  animation: slideDown 0.5s ease-in-out forwards;
  box-shadow: 0px 2px 12px 0px #0b0219;
  opacity: 1;
  transform: scale(1) translateY(0);
  width: 488px;

  &:not(:hover) {
    opacity: 1;
    transform: scale(1) translateY(0);
  }

  &__heading {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 24px;
    border-bottom: 1px solid #413c4d;
    margin-bottom: 24px;
    font-size: 24px;
    font-weight: 800;
    line-height: 42px;

      img{
        &:hover{
          cursor: pointer;
        }
        @include rtl-styles{
          transform: rotateY(180deg);
        }
      }
      
    h3 {
      margin: 0;
      font-family: $bricolage-font-family;
      font-optical-sizing: none;
      font-size: 24px;
      font-style: normal;
      font-weight: 800;
      line-height: 34px;

      @include rtl-styles {
        font-family: $arabic-font-family;
      }

      span {
        font-family: $default-font-family;

        @include rtl-styles {
          font-family: $arabic-font-family;
          direction: ltr;
        }
      }
    }

    img {
      @include rtl-rotate;
    }
  }

  &__description {
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 130%;
    margin: 0;
    min-height: 42px;
    max-height: 42px;
    overflow-y: scroll;

    @include rtl-styles{
      font-family: $arabic-font-family;
  }

    &::-webkit-scrollbar {
      display: none;
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
  }

  &__tag-section {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-top: 40px;
    overflow-x: scroll;
    cursor: pointer;

    @include rtl-styles{
      font-family: $arabic-font-family;
  }

    &::-webkit-scrollbar {
      display: none;
      -ms-overflow-style: none;
      scrollbar-width: none;
    }

    &__tag {
      padding: 4px 8px;
      display: flex;
      align-items: center;
      height: 24px;
      border-radius: 16px;
      font-size: 10px;
      font-style: normal;
      font-weight: 500;
      line-height: 140%;
      letter-spacing: 0.4px;
      text-transform: uppercase;
      min-width: fit-content;
      overflow: hidden;
    }
  }
}

.inactive-card {
  height: 260px;
  width: 488px;
  // width: 100%;
  padding: 24px;
  border-radius: 24px;
  position: absolute;
  text-align: bottom;
  display: flex;
  align-items: end;
  cursor: pointer;
  font-size: 24px;
  font-weight: 800;
  line-height: 34px;
  
  @include rtl-styles {
    font-family: $arabic-font-family;
  }

  &:hover {
    transform: scale(1.02);
    transition: all 0.5s ease-in-out;
  }

  &.animating {
    h3 {
      animation: fadeIn 10s ease-in-out;
    }
  }
}

.cards-container {
  position: relative;
  width: 100%;
  min-height: 488px;
  max-height: 488px;
}

.brands {
  margin-bottom: 75px;
}

.ignore-elem{
  display: contents;
}