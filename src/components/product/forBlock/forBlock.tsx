"use client";
import { useTranslation } from "next-i18next";
import styles from "./forBlock.module.scss";
import { CUSTOMER_TYPE } from "@constants/common";
import BackButton from "../backButton/backButton";

const ForBlock = ({ customerType, isMweb }: any) => {
  const { t } = useTranslation();

  return (
    <div className={isMweb ? styles["mweb-for-block"] : styles["for-block"]}>
      <div className={styles["heading"]}>
        <BackButton />
        <h2>
          {customerType === CUSTOMER_TYPE.BRANDS
            ? t("forBrands")
            : t("forBuyers")}
        </h2>
      </div>

      <p>
        {customerType === CUSTOMER_TYPE.BRANDS
          ? t("brandsDescription")
          : t("buyersDescription")}
      </p>
    </div>
  );
};

export default ForBlock;
