@import '@styles/variables';
@import '@styles/mixins';

.for-block{
    margin-top: 40px;
    h2{
        color: #C2B8FF;
        font-family: "Bricolage Grotesque";
        font-size: 40px;
        font-style: normal;
        font-weight: 800;
        line-height: 42px; /* 105% */
        letter-spacing: -1px;

        @include rtl-styles {
            font-family: $arabic-font-family;
          }
    }
    p{
        color: rgba(194, 184, 255, 0.80);
        font-family: "Mona Sans";
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 25.2px; /* 157.5% */
        width: 488px;
        margin-top: 16px;
        @include rtl-styles {
            font-family: $arabic-font-family;
          }
    }
}

.mweb-for-block{
    margin-top: 8px;
    h2{
        color: #C2B8FF;
        font-family: "Bricolage Grotesque";
        font-size: 24px;
        font-style: normal;
        font-weight: 800;
        line-height: 32px;
        margin-bottom: 8px;

        @include rtl-styles {
            font-family: $arabic-font-family;
          }
    }
    p{
        color: rgba(194, 184, 255, 0.80);
        font-family: "Mona Sans";
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        line-height: 16px; 
        @include rtl-styles {
            font-family: $arabic-font-family;
          }
    }
}
.heading{
    display: flex;
    align-items: center;
    gap: 16px;
}