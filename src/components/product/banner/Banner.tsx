"use client";

import React from "react";
import getConfig from "next/config";
import styles from "./Banner.module.scss";
import { imageBaseUrl } from "@constants/envVariables";
import { useTranslation } from "next-i18next";
import BackButton from "../backButton/backButton";

const Banner = ({ children }: any) => {
  const { t, i18n } = useTranslation();

  const backgroundImage = `${imageBaseUrl}/images/solutionHubBg.png`;
  const globe = `${imageBaseUrl}/images/brandU.svg`;
  const locale = i18n.language;

  return (
    <>
      <style jsx>{`
        .banner {
          background: url("${backgroundImage}");
          background-repeat: no-repeat;
          background-size: cover;
        }
        .blobe {
          background: url("${globe}");
          background-repeat: no-repeat;
          background-size: auto !important;
        }
      `}</style>
      <div className={`banner container ${styles["banner"]}`}>
        <div className={`blobe ${styles["banner__container"]}`}>
          {children}
        </div>
      </div>
    </>
  );
};

export default Banner;