'use client';

import { imageBaseUrl } from '@constants/envVariables';
import styles from './backButton.module.scss';
import { useRouter } from 'next/navigation';
import { useEffect, useRef } from 'react';
import { useTranslation } from 'next-i18next';

const BackButton = () => {
    const backImg = `${imageBaseUrl}/images/back.svg`;
    const router = useRouter();
    const containerRef = useRef<HTMLDivElement>(null);
    const {t} = useTranslation();


    // useEffect(() => {
    //     containerRef.current?.scrollIntoView();
    // },[]);

    const btnClickHandler = () => {
        router.push('/');
    }

    return(
        <div ref={containerRef} className={styles['back-button']} onClick={btnClickHandler}>
            <img src={backImg} />
            {/* <span>{t('back')}</span> */}
        </div>
    )
}

export default BackButton;