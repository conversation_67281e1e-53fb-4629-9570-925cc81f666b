.sliderContainer {
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.navButton {
  background-color: transparent;
  border: none;
  cursor: pointer;
  font-size: 20px;
  z-index: 1;
}

.slider {
  display: flex;
  scroll-behavior: smooth;
  gap: 30px;
  width: 100%;

  animation: scroll-ltr 15s linear infinite;
  animation-delay: 1s;

  [dir="rtl"] & {
      animation: scroll-rtl 15s linear infinite;
      animation-delay: 1s;
  }

  &:hover{
    animation-play-state: paused;
  }
}

.slider::-webkit-scrollbar {
  display: none; /* Hide scrollbar */
}

.sliderImage {
  flex: 0 0 auto;
  width: 100px;
  height: 30px;
  object-fit: cover;
  border-radius: 8px;
}

/* Add blur effect on both sides */
.sliderContainer::before,
.sliderContainer::after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  width: 50px;
  z-index: 1;
  pointer-events: none;
}

.sliderContainer::before {
  left: -6px;
  background: linear-gradient(90deg, #10071D, transparent);
  border-top-left-radius: 12px;
  border-bottom-left-radius: 12px;
}

.sliderContainer::after {
  right: -6px;
  background: linear-gradient(270deg, #10071D, transparent);
  border-top-right-radius: 12px;
  border-bottom-right-radius: 12px;
}

/* Animation for LTR marquee scrolling */
@keyframes scroll-ltr {
  0% {
      transform: translateX(0%);
  }

  100% {
      transform: translateX(-100%);
  }
}

/* Animation for RTL marquee scrolling */
@keyframes scroll-rtl {
  0% {
      transform: translateX(0%);
  }

  100% {
      transform: translateX(100%);
  }
}