import React, { useRef } from "react";
import styles from "./Slider.module.scss";
import { imageBaseUrl } from "@constants/envVariables";
import MetricCard from "components/cardInfo/metricCard/metricCard";

type SliderProps = {
  data: any;
};

const Slider: React.FC<SliderProps> = ({data}) => {

  return (
    <div className={styles.sliderContainer}>
      <div className={styles.slider}>
        {data?.map((d: any, index: any) => (
          <MetricCard key={index} title={d?.title} subtitle={d?.subTitle} image={d?.image} />
        ))}
      </div>
    </div>
  );
};

export default Slider;