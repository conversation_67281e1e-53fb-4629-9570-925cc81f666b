@import "@styles/variables";
@import "@styles/mixins";

.banner-text {
  text-align: center;
  // padding-top: 140px;
  position: absolute;
  &__title {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    font-weight: 800;
    font-family: $bricolage-font-family;
    font-optical-sizing: none;
    gap: 0.5rem;
    color: #fff;
    line-height: 110%; 

    @include rtl-styles {
      font-family: $arabic-font-family;
    }

    span {
      display: inline-block;
      font-size: 88px;
      font-weight: 800;
      line-height: 110%; 
      background: linear-gradient(117deg, #C2B8FF 13.69%, #FFF 52.65%, #C2B8FF 87.35%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  &__sub-title {
    font-size: 24px;
    opacity: 0;
    color: #c2b8ff;
    max-width: 600px;
    margin: 8px auto 0 auto;
    font-style: normal;
    font-weight: 500;
    line-height: 150%; /* 36px */
    font-family: $default-font-family;

    @include rtl-styles {
      margin: 16px auto 0 auto;
    }
  }
}

.mweb-banner-text {
  text-align: center;
  padding-top: 140px;
  font-family: $bricolage-font-family;

  .banner-text__title {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    font-size: 2.5rem;
    font-weight: bold;
    gap: 0.5rem;

    span {
      display: inline-block;
      font-size: 40px;
      font-weight: 800;
      background: linear-gradient(117deg, #C2B8FF 13.69%, #FFF 52.65%, #C2B8FF 87.35%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .banner-text__sub-title {
    font-size: 14px;
    opacity: 0;
    color: #c2b8ff;
    max-width: 296px;
    font-weight: 500;
    line-height: 18px;
    letter-spacing: -0.14px;
    margin: 16px auto 0 auto;
    padding-bottom: 40px;
  }
}