"use client";

import React, { useEffect, useRef } from "react";
import gsap from "gsap";
import styles from "./AnimatedTitleWithSubtitle.module.scss";
import { spawn } from "child_process";

type Props = {
  title: string;
  subtitle: string[];
  locale: string;
  isMweb?: boolean;
};

const AnimatedTitleWithSubtitle: React.FC<Props> = ({
  title,
  subtitle,
  locale,
  isMweb
}) => {
  const titleRef = useRef<HTMLDivElement | null>(null);
  const subtitleRef = useRef<HTMLDivElement | null>(null);
  const isRTL = locale === "ar";

  useEffect(() => {
    if (!titleRef.current || !subtitleRef.current) return;

    const words = titleRef.current.querySelectorAll("span");

    // Animate the main title
    gsap.fromTo(
      words,
      { y: 3, opacity: 0, filter: "blur(8px)" },
      {
        y: 0,
        opacity: 1,
        duration: 0.9,
        stagger: { each: 1, from: "start" },
        filter: "blur(0px)",
        onComplete: () => {
          gsap.fromTo(
            subtitleRef.current,
            { y: 20, opacity: 0 },
            { y: 0, opacity: 1, duration: 0.6 }
          );
        },
      }
    );
  }, []);

  return (
    <div className={isMweb ? styles['mweb-banner-text'] : styles["banner-text"]}>
      {/* Main Title */}
      <div dir={isRTL ? 'rtl' :'ltr'}className={styles["banner-text__title"]} ref={titleRef}>
        {title.split(" ").map((word, i) => (
          <span key={i}>{word}</span>
        ))}
      </div>

      {/* Subtitle */}
      <div className={styles["banner-text__sub-title"]} ref={subtitleRef}>
        {subtitle.map((sub, i) => (
          <span key={i}>
            {sub} <br />{" "}
          </span>
        ))}
      </div>
    </div>
  );
};

export default AnimatedTitleWithSubtitle;
