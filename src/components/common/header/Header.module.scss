@import "@styles/variables";
@import "@styles/mixins";

.header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  transition: margin 0.2s ease-in-out;
  margin: 40px auto 40px auto;

  &__left-side {
    display: flex;
    justify-content: center;
    align-items: center;

    > img {
      height: 48px;
      cursor: pointer;
    }
  }

  &__solutionHub {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 10px;
    justify-content: center;
    align-items: center;

    span {
      color: #c2b8ff;
      font-weight: 600;
      font-size: 16px;

      @include rtl-styles{
        font-family: $arabic-font-family;
    }
    }
  }

  &__divider {
    height: 80%;
    width: 1px;
    background: rgba(255, 255, 255, 0.5);
    margin: 0 16px 0 24px;
  }

  &__talk-button {
    display: grid !important;
    grid-template-columns: 1fr auto;
    grid-gap: 10px;
    padding: 12px 24px !important;
    font-size: 16px;
    font-weight: 600;
    border-radius: 48px !important;
    border: 1px solid #8679d5 !important;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    background: linear-gradient(0deg, $lavender 0%, $lavender 100%), #0f0f0f;
    font-family: $default-font-family;

    @include rtl-styles {
      font-family: $arabic-font-family;
    }

    div {
      height: 24px;
    }

    p {
      margin: 0;
      color: #0f0f0f;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 20px;
    }
  }

  &__right-side {
    display: flex;
    gap: 16px;
    align-items: center;
  }

  &__lang-switch {
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    border: 1px solid #413c4d;
    padding: 8px 16px;
    border-radius: 100px;
    font-size: 14px;
    font-weight: 600;

    img {
      width: 24px;
      height: 24px;
    }
  }

  &__scroll {
    backdrop-filter : blur(10px);
    transition: backdrop-filter 0.5s ease-in-out;
    .header {
      margin-top: 25px !important;
      margin-bottom: 25px !important;
      transition: margin 0.3s ease-in-out;
    }
  }
}

.header-container {
  position: fixed;
  width: 100%;
  z-index: 999;
  top: 40px;
}
