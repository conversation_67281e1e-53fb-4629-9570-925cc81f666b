"use client";

import React, { useEffect, useRef, useState } from "react";
import styles from "./Header.module.scss";
import { imageBaseUrl } from "@constants/envVariables";
import { useTranslation } from "next-i18next";
import Link from "next/link";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { Modal, Box } from "@mui/material";
import ContactUs from "components/home/<USER>/ContactUs";

const Header = () => {
  const { t, i18n } = useTranslation();
  const router = useRouter();
  const pathname = usePathname();
  const locale = i18n.language;
  const [openModal, setOpenModal] = useState(false);
  const searchParams = useSearchParams()

  const [isScrolled, setIsScrolled] = useState(false);

  const logo = `${imageBaseUrl}/images/logo.svg`;
  const cube = `${imageBaseUrl}/images/cube.svg`;
  const handShake = `${imageBaseUrl}/images/handShake.svg`;
  const TranslateIcon = `${imageBaseUrl}/images/language-switch-white.svg`;

  const handleOpenModal = () => {
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
  };

  const switchLanguage = () => {
    const newLocale = locale === "ar" ? "en" : "ar";
    const segments = pathname.split("/");
    segments[1] = newLocale;
    const newPath = segments.join("/");
    router.push(newPath);
  };

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 100);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    if (searchParams.has('inquire-now')) {
      handleOpenModal();
    }
  }, [searchParams]);

  return (
    <div className={`${styles["header-container"]} ${isScrolled ? styles["header__scroll"] : ""}`}>
      <div className={`container ${styles["header"]}`}>
        <div className={styles["header__left-side"]}>
          <Link href={`/${locale}/`}>
            <img src={logo} alt="yougotagift-logo" />
          </Link>
          <div className={styles["header__divider"]}></div>
          <div className={styles["header__solutionHub"]}>
            <img src={cube} alt="solutionHub-logo" />
            <span>{t("solutionHub")}</span>
          </div>
        </div>
        <div className={styles["header__right-side"]}>
          <div
            onClick={switchLanguage}
            className={styles["header__lang-switch"]}
          >
            {locale == "ar" ? "EN" : "AR"}
            <img src={TranslateIcon} alt={""} />
          </div>
          <button
            className={styles["header__talk-button"]}
            onClick={handleOpenModal}
          >
            <div>
              <img src={handShake} alt="shake logo" />
            </div>
            <p> {t("talkToExpert")} </p>
          </button>
        </div>
      </div>

      {/* Contact Us Modal */}
      <Modal
        open={openModal}
        disableEscapeKeyDown
        disableAutoFocus
        disableEnforceFocus
        disableRestoreFocus
        disablePortal={false}
        hideBackdrop={false}
        disableScrollLock={false}
        onClose={handleCloseModal}
        aria-labelledby="contact-us-modal"
        aria-describedby="contact-us-form-modal"
        slotProps={{
          backdrop: {
            className: "modal__backdrop",
          },
        }}
      >
        <Box className="modal__content">
          <ContactUs onClose={handleCloseModal} />
        </Box>
      </Modal>
    </div>
  );
};

export default Header;
