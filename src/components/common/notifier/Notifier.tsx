import styles from "./Notifier.module.scss";
import getConfig from "next/config";
import { TOAST_TYPE } from "@constants/common";
import { imageBaseUrl } from "@constants/envVariables";

export const icons: any = {
  checkOutline: (
    <img
      src={`${imageBaseUrl}/images/success-tick.svg`}
      alt="icon"
      height={24}
      width={24}
      className={`${styles["circle"]} pulse-success`}
    />
  ),
  errorOutline: (
    <img
      src={`${imageBaseUrl}/images/error-tick.svg`}
      alt="icon"
      height={24}
      width={24}
      className={`${styles["circle"]} pulse-error`}
    />
  ),
};

const NotifierBody = ({ icon, title, description, type = null }: any) => {
  return (
    <div
      className={`${styles["notifier"]} notifier  ${
        type === TOAST_TYPE.CAPSULE ? styles["capsule-notifier"] : ""
      } `}
    >
      <div
        className={`${styles["notifier__container"]} ${
          type === TOAST_TYPE.CAPSULE ? styles["capsule-notifier__cont"] : ""
        }`}
      >
        {icon && (
          <div className={styles["notifier__icon"]} data-testid="notiIcon">
            {icons[icon]}
          </div>
        )}
        <div className={styles["notifier__text-section"]}>
          {title?.length > 1 && (
            <div className={styles["notifier__title"]} data-testid="notiTitle">
              {title}
            </div>
          )}

          <div
            className={styles["notifier__description"]}
            data-testid="notiDescription"
          >
            {description}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotifierBody;
