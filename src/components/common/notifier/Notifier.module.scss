@import "@styles/variables";
@import "@styles/mixins";

.notifier {
  position: fixed;
  top: 34px;
  left: 50%;
  z-index: 9999;
  border-radius: 6px;
  color: #fff;

  @include for-mobile-phone-only {
    top: 30px;
    left: 0;
    border-radius: 12px;
    width: 100%;
    padding: 0 16px !important;
  }

  &__container {
    min-width: 200px;
    min-height: 56px;
    max-width: 800px;
    background-color: #0B0219;
    position: relative;
    left: -50%;
    border-radius: 12px;
    padding: 12px 16px;
    display: flex;
    gap: 16px;
    align-items: center;

    @include for-mobile-phone-only {
      min-width: 100%;
      min-height: 70px;
      left: 0;
      border-radius: 12px;
      padding: 0 16px !important;
    }
  }

  &__icon {
    display: flex;
    align-items: center;
  }

  &__text-section {
    display: flex;
    flex-direction: column;
    gap: 7px;
  }

  &__title {
    font-size: 18px;
    font-weight: 700;
  }

  &__description {
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px;
    letter-spacing: -0.14px;
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.capsule-notifier {
  position: fixed;
  bottom: 64px;
  border-radius: 12px;

  &__cont {
    position: absolute;
    padding: 8px 16px !important;
    min-width: 160px !important;
    max-width: 400px !important;
    min-height: 46px;
    bottom: 187px;
    left: 50%;
    transform: translate(-50%);
    display: flex;
    justify-content: center;

    @include for-mobile-phone-only {
      bottom: 26px;
      min-width: unset !important;
    }
  }
}

.circle {
  height: 24px;
  width: 24px;
  border-radius: 50%;
}
