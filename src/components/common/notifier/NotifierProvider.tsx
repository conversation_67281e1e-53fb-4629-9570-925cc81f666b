"use client";

import React, { useState, useEffect, useCallback } from "react";
import NotifierBody from "./Notifier";
import { NotifierContext } from "./NotifierContext";

const NotifierProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [notifierState, setNotifierState] = useState<any>({
    title: "",
    description: "",
    icon: null,
    autoHideDuration: 5000,
    onClose: undefined,
    type: null,
  });

  const [showNotifier, setShowNotifier] = useState(false);

  const showNotification = useCallback((data: any) => {
    setNotifierState({ ...data });
  }, []);

  useEffect(() => {
    if (notifierState.description) {
      setShowNotifier(true);
      const timeout = setTimeout(() => {
        setShowNotifier(false);
        notifierState.onClose?.();
        setNotifierState({
          title: "",
          description: "",
          icon: null,
          autoHideDuration: 5000,
          onClose: undefined,
          type: null,
        });
      }, notifierState.autoHideDuration || 5000);

      return () => clearTimeout(timeout);
    }
  }, [notifierState.description]);

  return (
    <NotifierContext.Provider value={{ showNotification }}>
      {children}
      {showNotifier && (
        <NotifierBody
          title={notifierState.title}
          description={notifierState.description}
          icon={notifierState.icon}
          type={notifierState.type}
        />
      )}
    </NotifierContext.Provider>
  );
};

export default NotifierProvider;
