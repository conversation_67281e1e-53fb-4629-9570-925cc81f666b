import React, { createContext, useC<PERSON>back, useContext, useState } from "react";
import { TOAST_TYPE } from "@constants/common";

interface NotifierData {
  title: string;
  description: string;
  icon: any;
  autoHideDuration?: number;
  type?: string | null;
  onClose?: () => void;
}

interface NotifierContextProps {
  showNotification: (data: NotifierData) => void;
}

export const NotifierContext = createContext<NotifierContextProps | undefined>(
  undefined
);

export const useNotifier = () => {
  const context = useContext(NotifierContext);
  if (!context) {
    throw new Error("useNotifier must be used within a NotifierProvider");
  }
  return context;
};
