"use client";

import React from "react";
import styles from "./HeroNote.module.scss";
import { useTranslation } from "next-i18next";

const HeroNote = () => {
  const { t } = useTranslation();
  return (
    <div className={styles["heroNote"]}>
      <span className={styles["heroNote__badge"]}>NEW</span>
      <div className={styles["heroNote__list"]}>
        <span className={styles["heroNote__item"]}> {t("newtWork")}</span>
        <span className={styles["heroNote__item"]}> {t("clients")}</span>
        <span className={styles["heroNote__item"]}> {t("majorPrograms")}</span>
      </div>
    </div>
  );
};

export default HeroNote;
