@import "@styles/variables";
@import "@styles/mixins";

.heroNote {
  text-align: center;
  background-color: $lavender;
  padding: 8px 0;
  position: fixed;
  width: 100%;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: $default-font-family;

  @include rtl-styles {
    font-family: $arabic-font-family;
  }

  @media (max-width: 768px) {
    display: none;
  }

  &::before,
  &::after {
    content: "";
    height: 24px;
    width: 24px;
    background: transparent;
    position: absolute;
    bottom: -24px;
    border-top-left-radius: 50px;
    box-shadow: -5px -6px 0px 2px $lavender;
    z-index: 9999;
  }

  &::before {
    left: 0px;
  }

  &::after {
    right: 0px;
    transform: scaleX(-1);
  }

  &__badge {
    background: $jet-black;
    color: $lavender;
    font-size: 12px;
    font-weight: 700;
    text-transform: capitalize;
    padding: 0px 7px;
    border-radius: 4px;
    width: auto;
    display: inline;
    margin-right: 14px;
    line-height: 24px;

    @include rtl-styles {
      margin-left: 14px;
      margin-right: 0px;
    }
  }

  &__list {
    font-size: 15px;
    color: $jet-black;
    font-weight: 500;
  }

  &__item {
    padding-inline-end: 13px;

    @include rtl-styles {
      padding-right: 0px;
      padding-left: 13px;
    }

    &::after {
      content: "•";
      color: #b800c4;
      position: absolute;
      border-radius: 100px;
      margin-left: 5px;

      @include rtl-styles {
        margin-left: 0px;
        margin-right: 5px;
      }
    }

    &:last-child {
      &::after {
        content: "";
      }
    }
  }
}
