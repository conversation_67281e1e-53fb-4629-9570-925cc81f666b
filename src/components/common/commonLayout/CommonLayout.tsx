import React from "react";
import HeroNote from "../heroNote/HeroNote";
import Footer from "../footer/Footer";
import styles from "./CommonLayout.module.scss";

const CommonLayout = ({ children }: any) => {
  return (
    <div className={styles["layout"]}>
      <HeroNote />
      <div className={styles["layout__wrapper"]}>
        {children}
        <div className={styles["layout__footer-wrap"]}>
          <Footer />
        </div>
      </div>
    </div>
  );
};

export default CommonLayout;
