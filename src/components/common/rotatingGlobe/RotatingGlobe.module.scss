@import "@styles/variables";
@import "@styles/mixins";

.globe-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 1200px;
  height: 600px;
  overflow: hidden;
  position: relative;
}

.globe-canvas {
  display: block;
  transition: transform 0.3s ease;
  
  // &:hover {
  //   transform: scale(1.05);
  // }
}

// Responsive adjustments
@media (max-width: 768px) {
  .globe-container {
    padding: 20px;
  }
  
  .globe-canvas {
    max-width: 300px;
    max-height: 300px;
  }
}

@media (max-width: 480px) {
  .globe-canvas {
    max-width: 250px;
    max-height: 250px;
  }
}
