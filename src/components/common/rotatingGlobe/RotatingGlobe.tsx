"use client";

import React, { useEffect, useRef } from "react";
import createGlobe from "cobe";
import styles from "./RotatingGlobe.module.scss";
import AnimatedTitleWithSubtitle from "../animatedTitleWithSubtitle/AnimatedTitleWithSubtitle";
import { useTranslation } from "react-i18next";

interface RotatingGlobeProps {
  className?: string;
}

const RotatingGlobe: React.FC<RotatingGlobeProps> = ({ className = "" }) => {
  const size = 1200;
  const height = 1200;
  const devicePixelRatio = 2;
  const initialPhi = 0;
  const theta = 0.1;
  const dark = 1;
  const diffuse = 0;
  const mapSamples = 20000;
  const mapBrightness = 8.0;
  const mapBaseBrightness = 0.0;
  const baseColor: [number, number, number] = [0.1, 0.1, 0.1];
  const markerColor: [number, number, number] = [0, 0, 0];
  const glowColor: [number, number, number] = [0.1, 0.1, 0.1];
  const scale = 1;
  const offset: [number, number] = [0, 1000];
  const opacity = 0.3;
  const markers: Array<{ location: [number, number]; size: number }> = [];
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const globeRef = useRef<any>(null);

  useEffect(() => {
    let currentPhi = initialPhi;

    if (canvasRef.current) {
      globeRef.current = createGlobe(canvasRef.current, {
        devicePixelRatio,
        width: size * 2,
        height: size * 2,
        phi: currentPhi,
        theta,
        dark,
        diffuse,
        scale,
        mapSamples,
        mapBrightness,
        mapBaseBrightness,
        baseColor,
        markerColor,
        glowColor,
        offset,
        markers,
        opacity,
        onRender: (state) => {
          currentPhi -= 0.003;
          state.phi = currentPhi;
        },
      });
    }

    return () => {
      if (globeRef.current) {
        globeRef.current.destroy();
      }
    };
  }, [
    size,
    devicePixelRatio,
    initialPhi,
    theta,
    dark,
    diffuse,
    scale,
    mapSamples,
    mapBrightness,
    mapBaseBrightness,
    baseColor,
    markerColor,
    glowColor,
    offset,
    markers,
    opacity,
  ]);

  const { t, i18n } = useTranslation();
  const locale = i18n.language;

  const subTitles = [t("bannerSubTitle1"), t("bannerSubTitle2")];
  return (
    <div className={`${styles["globe-container"]} ${className}`}>
      <canvas
        ref={canvasRef}
        style={{
          width: size,
          height: height,
          maxWidth: "100%",
          aspectRatio: 1,
        }}
        className={styles["globe-canvas"]}
      />
      <AnimatedTitleWithSubtitle
        title={t("solutionHub")}
        subtitle={subTitles}
        locale={locale}
      />
    </div>
  );
};

export default RotatingGlobe;
