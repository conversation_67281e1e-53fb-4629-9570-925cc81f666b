import { imageBaseUrl } from "@constants/envVariables"

const PreloadImages = () => {
    return(
        <>
            <link rel="preload" as="image" href={`${imageBaseUrl}/images/giftCardsMweb.png`} />
            <link rel="preload" as="image" href={`${imageBaseUrl}/images/brandPageMweb.svg`} />
            <link rel="preload" as="image" href={`${imageBaseUrl}/images/ggMweb.svg`} />
            <link rel="preload" as="image" href={`${imageBaseUrl}/images/add-Media.svg`} />
            <link rel="preload" as="image" href={`${imageBaseUrl}/images/walletMweb.svg`} />
            <link rel="preload" as="image" href={`${imageBaseUrl}/images/work-pro.png`} />
            <link rel="preload" as="image" href={`${imageBaseUrl}/images/assisted-sales.png`} />
            <link rel="preload" as="image" href={`${imageBaseUrl}/images/custom-designs.png`} />
            <link rel="preload" as="image" href={`${imageBaseUrl}/images/loyalty-frame.png`} />
        </>
    )
}

export default PreloadImages;