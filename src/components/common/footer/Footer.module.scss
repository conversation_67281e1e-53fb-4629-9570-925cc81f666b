@import "@styles/variables";
@import "@styles/mixins";

.footer {
  background: #13072d;
  padding: 40px;
  border-radius: 24px;
  font-family: $bricolage-font-family;
  font-weight: 500;

  @include rtl-styles {
    font-family: $arabic-font-family;
  }

  &__cta {
    display: flex;
    justify-content: center;
    align-items: center;
    max-height: 122px;
    background: #0b0219;
    border-radius: 24px;
    overflow: hidden;
    height: 122px;

    .parent {
      display: flex;
      flex: 1;
      font-size: 40px;
      font-weight: 800;
      letter-spacing: -1px;
      cursor: pointer;
    }
  }

  &__cta-right {
    width: 100%;
    padding: 40px;
  }

  &__cta-left {
    width: 100%;
    border-radius: 24px 0px 0px 24px;
    padding: 0 40px;
    display: flex;
    align-items: center;
    display: flex;
    flex: 1;
    font-size: 40px;
    font-weight: 800;
    letter-spacing: -1px;
    color: $lavender;
  }

  &__cta-container {
    width: 100%;
  }

  &__cta-right &__label-container {
    position: relative;
    z-index: 1;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    &--arrow{
      @include rtl-styles {
        transform: rotateY(180deg);
      }
    }
  }

  &__cta-right &__label-container::before,
  &__cta-right &__label-container::after {
    content: "";
    position: absolute;
    height: 612px;
    left: -40px;
    right: -40px;
    background: $lavender;
    z-index: 1;
    top: -190px;
    border-radius: 50% 0 0 50%;
    bottom: 0;

    @include rtl-styles {
      border-radius: 0 50% 50% 0;
    }
  }

  &__cta-right &__label-container::after {
    z-index: 0;
    opacity: 0.1;
    left: -56px;

    @include rtl-styles {
      right: -56px;
      left: -40px;
    }
  }

  &__cta-label{
    span{
      font-family: "Mona Sans";
      font-size: 32px;
      font-style: normal;
      font-weight: 700;
      line-height: 120%;

      @include rtl-styles {
        font-family: $arabic-font-family;
      }
    }
    
  }

  &__cta-right &__label-container &__cta-label {
    position: absolute;
    color: $jet-black;
    z-index: 999;
    display: flex;
    align-items: center;
    gap: 11px;

    img {
      width: 40px;
      height: 40px;
    }

    i {
      width: 32px;
      height: 32px;
      margin-left: 5px;

      @include rtl-styles {
        margin-left: 0px;
        margin-right: 5px;
      }
    }
  }

  &__content {
    margin-top: 48px;
    color: rgba(255, 255, 255, 0.6);
  }

  &__brand-logo {
    img {
      width: 54px;
      height: 54px;
    }
    margin-bottom: 32px;
  }

  &__content-row {
    display: flex;
    justify-content: space-between;
    margin-top: 40px;
  }

  &__copyright-container {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 32px;
    font-size: 14px;
    color: rgba(194, 184, 255, 0.8);
    font-family: $default-font-family;

    @include rtl-styles {
      font-family: $arabic-font-family;
    }
  }

  &__copyright {
    @media (max-width: ($md + 40)) {
      order: 2;
      margin-top: 23px;
    }
  }

  &__tagline p {
    margin-top: 0;
    font-size: 14px;
    font-family: $default-font-family;

    @include rtl-styles {
      font-family: $arabic-font-family;
    }
  }

  &__available-regions {
    font-size: 14px;
    color: rgba(194, 184, 255, 0.8);
    font-weight: 500;
    font-family: $default-font-family;

    @include rtl-styles {
      font-family: $arabic-font-family;
    }

    @media (max-width: ($md + 40)) {
      order: 1;
    }

    .sep {
      font-weight: 200;
      display: inline-block;
      padding: 0 3px;
      padding-inline-start: 5px;
    }
  }

  &__content-column {
    gap: 110px;
    display: flex;
    justify-content: space-between;

    h5 {
      font-size: 18px;
      color: #c2b8ff;
      font-family: "Bricolage Grotesque";
      letter-spacing: -0.09px;
      font-weight: 800;

      @include rtl-styles {
        font-family: $arabic-font-family;
      }
    }

    p {
      font-size: 16px;
      font-family: "Mona Sans";
      margin-top: 24px;
      color: rgba(194, 184, 255, 0.8);

      @include rtl-styles {
        font-family: $arabic-font-family;
      }
    }
  }

  &__social-icons {
    display: flex;
    gap: 16px;
    margin-top: 43px;

    img {
      width: 40px;
      height: 40px;
    }
  }

  &__menu {
    padding-inline-end: 50px;
    h5 {
      color: $lavender;
      font-size: 18px;
      font-weight: 800;
      letter-spacing: 0.4px;
      margin-bottom: 24px;
      margin-top: 0;
    }
  }
  &__item-list {
    font-size: 14px;
    font-weight: 400;
    display: flex;
    flex-direction: column;
    gap: 20px;
    color: rgba(194, 184, 255, 0.8);
    font-size: 16px;
    font-weight: 500;
    font-family: $default-font-family;

    @include rtl-styles {
      font-family: $arabic-font-family;
    }
  }
}

.hiring-align {
  display: flex !important;
  gap: 8px !important;
  align-items: center;
}

.modal {
  &__backdrop {
    background-color: rgba(0, 0, 0, 0.3) !important;
    position: relative;
  }

  &__content {
    position: absolute;
    top: 600px;
    right: -230px;
    // transform: translate(-50%, -50%);
    outline: none;
    animation: slideInFromRight 0.5s ease-out forwards;

    @include rtl-styles {
      right: auto;
      left: 260px;
      animation: slideInFromLeft 0.5s ease-out forwards;
    }
  }
}

@keyframes slideInFromLeft {
  0% {
    transform: translate(-100%, -50%);
    opacity: 0;
  }
  100% {
    transform: translate(-50%, -50%);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  0% {
    transform: translate(100%, -50%);
    opacity: 0;
  }
  100% {
    transform: translate(-50%, -50%);
    opacity: 1;
  }
}
