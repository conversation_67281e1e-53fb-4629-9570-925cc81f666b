@import "@styles/variables";
@import "@styles/mixins";

.country-phone-code {
  display: flex;
  &-container {
    display: flex;
    @include rtl-styles {
      gap: 5px;
      align-items: center;

      > span + span {
        unicode-bidi: embed;
        direction: ltr;
      }
    }
  }
  .country-select {
    width: 100% !important;
    // color: $jet-black !important;
    margin-right: 2px;
  }

  &__menu-item {
    padding: 12px 16px !important;
    display: grid !important;
    grid-template-columns: 24px 1fr 50px;
    gap: 10px;
    min-height: 23px !important;
    font-size: 14px !important;
    font-family: $default-font-family !important;
    margin-bottom: 8px;

    &:hover {
      background-color: $grey-primary !important;
      border-radius: $border-radius-min;
    }

    > span {
      // color: $jet-black !important;
      white-space: break-spaces;
    }
  }

  &__menu-item-send_to {
    padding: 7px;
    display: grid;
    grid-template-columns: 70px auto;
    gap: 10px;
    min-height: 23px;
    font-size: 14px;

    &:hover {
      background-color: $light-purple;
      border-radius: $border-radius-min;
    }

    > div {
      > img {
        width: 30px;
        height: 30px;
        object-fit: contain;
        border-radius: 3px;
      }

      > span {
        font-size: 20px;
      }
    }
  }

  &__search-box {
    padding: 16px 16px 8px 16px;
  }

  &__search-input {
    div {
      padding-left: 10px !important;

      @include rtl-styles {
        padding-right: 10px !important;
      }
    }

    input {
      padding: 0;
      height: 50px;
      min-height: 23px;
      border-radius: 8px;
      font-size: 14;

      @include rtl-styles {
        
      }

      &::placeholder {
        font-size: 14px;
        @include rtl-styles {
          text-align: right;
        }

        margin: 0 0 0 14px;
        opacity: 1 !important;
        font-weight: 500;
        font-stretch: normal;
        font-style: normal;
        line-height: 18px;
        letter-spacing: normal;
        text-align: left;
        color: $placeholder-gray;
      }
    }
  }

  &__search-icon {
    width: 24px;
    height: 24px;
    margin: 0 10px;
  }

  &__flag {
    width: 24px;
    height: 24px;

    > span {
      font-size: 14px;
    }

    img {
      max-height: 24px;
    }
  }

  &__code {
    text-align: right;

    @include rtl-styles {
      text-align: left;
    }
  }
}
