import {
  Bricolage_Grotesque,
  Noto_Kufi_Arabic,
  Mona_Sans
} from 'next/font/google';

// Bricolage Grotesque
export const bricolageGrotesque = Bricolage_Grotesque({
  // weight: ['400', '500', '600', '700', '800'],
  weight: 'variable',
  style: ['normal'],
  subsets: ['latin'],
  display: 'swap',
  axes: ["opsz"],
  variable: '--font-bricolage',
});

// Mona Sans
export const monaSans = Mona_Sans({
  weight: ['400', '500', '600', '700', '800'],
  style: ['normal'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-mona-sans',
});

// Noto Kufi Arabic
export const notoKufiArabic = Noto_Kufi_Arabic({
  weight: ['400', '500', '600', '700', '800'],
  subsets: ['arabic'],
  display: 'swap',
  variable: '--font-noto-kufi',
});
