import { NextRequest, NextResponse } from "next/server";
import crypto from "crypto";
import axios from "axios";
import getConfig from "next/config";

const { serverRuntimeConfig } = getConfig();
const {
  emailServiceApi,
  emailServiceApiKey,
  emailServiceApiSecret,
  emailServiceMailTo,
  emailServiceMailFrom,
  captchaSecretKey,
  captchaVerifyUrl,
  emialServiceTemplateCode
} = serverRuntimeConfig;

/**
 * Get required headers for mail request
 */
function getHeaders(apiKey: string): Record<string, string> {
  const date = new Date().toUTCString();
  return {
    Accept: "application/json",
    "X-Api-Key": apiKey,
    date,
  };
}

/**
 * Create the HTTPSignature Authorization header manually
 */
function getAuthHeader(
  apiKey: string,
  apiSecret: string,
  headers: Record<string, string>
): string {
  const signingString = `accept: ${headers["Accept"]}\ndate: ${headers["date"]}`;
  const hmac = crypto.createHmac("sha256", apiSecret);
  hmac.update(signingString);
  const signature = hmac.digest("base64");

  return `Signature keyId="${apiKey}",algorithm="hmac-sha256",headers="accept date",signature="${signature}"`;
}

async function sendMail(
  url: string,
  payload: any,
  apiKey: string,
  apiSecret: string
): Promise<any> {
  try {
    const headers = getHeaders(apiKey);
    const authHeader = getAuthHeader(apiKey, apiSecret, headers);

    const response = await axios.post(url, payload, {
      headers: {
        ...headers,
        Authorization: authHeader,
      },
    });

    if (response.status === 409) {
      return "RESOURCE_CONFLICT";
    }

    return response.data;
  } catch (error: any) {
    console.error("Error sending mail:", error?.message || error);
    throw error;
  }
}

//Verify reCAPTCHA token
async function verifyCaptcha(
  token: string
): Promise<{ success: boolean; data?: any }> {
  if (!token) return { success: false };

  try {
    const { data } = await axios.post(
      captchaVerifyUrl,
      new URLSearchParams({
        secret: captchaSecretKey,
        response: token,
      }).toString(),
      {
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
      }
    );
    console.log("verify captcha response ", data);

    return { success: data.success, data };
  } catch (error) {
    console.error("Captcha verification failed:", error);
    return { success: false };
  }
}

// Main API Handler
export async function POST(req: NextRequest) {
  try {
    const { message, name, email, phoneNumberVal, token, jobTitle, companyName } = await req.json();

    const captchaResult:any = await verifyCaptcha(token);

    if (!captchaResult?.success || captchaResult?.score < 0.4) {
      return NextResponse.json(
        { error: "Invalid Captcha", details: captchaResult.data },
        { status: 400 }
      );
    } else {
      const payload = {
        subject: "Enquiry - Global Lead Generation",
        mail_from: { email: emailServiceMailFrom, name: "Support" },
        mail_to: [{ email: emailServiceMailTo, name: "User" }],
        template_data: {
          subject: "Enquiry - Global Lead Generation",
          sub: "There has been an inquiry through the Global Lead Generation Form.",
          customerName: name,
          businessEmail: email,
          mobileNumber: phoneNumberVal,
          lookingFor: message,
          jobTitle: jobTitle,
          companyName: companyName
        },
        template_code: emialServiceTemplateCode,
      };

      const result = await sendMail(
        emailServiceApi,
        payload,
        emailServiceApiKey,
        emailServiceApiSecret
      );
      console.log("send email response", result);

      return NextResponse.json(result, { status: 200 });
    }
  } catch (error: any) {
    console.error("Error in POST handler:", error);
    return NextResponse.json(
      { error: "Failed to send email", details: error?.message },
      { status: 500 }
    );
  }
}

// Handle preflight request
export function OPTIONS() {
  return new Response(null, {
    status: 204,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}
