import type { Metadata } from "next";
import "@styles/globals.scss";
import TranslationsProvider from '../../i18n/TranslationsProvider';
import initTranslations from '../../i18n/i18n';
import { dir } from 'i18next';


import {
  bricolageGrotesque,
  notoKufiArabic,
  monaSans
} from './../font';
import NotifierProvider from "components/common/notifier/NotifierProvider";
import PreloadImages from "components/common/preloadImages/preloadImages";

export const metadata: Metadata = {
  title: "Solutions Hub",
  description: "Gift Card & Gift Voucher Solutions for Businesses in UAE, KSA & MENA - YOUGotaGift",
};
const i18nNamespaces = ['common'];

export default async function RootLayout({
  children,
  params
}: Readonly<{
  children: React.ReactNode;
  params: any;
}>) {
  const { lang } = await params;
  const { resources } = await initTranslations(lang, i18nNamespaces);

  return (
    <TranslationsProvider
      namespaces={i18nNamespaces}
      locale={lang}
      resources={resources}
    >
      <NotifierProvider>
        <html lang={lang} dir={dir(lang)}>
          <head>
            <PreloadImages />
          </head>
          <body
            className={`${bricolageGrotesque.variable} ${notoKufiArabic.variable} ${monaSans.variable}`}
          >
            {children}
          </body>
        </html>
      </NotifierProvider>
    </TranslationsProvider>
  );
}
