import Image from "next/image";
import { getTranslation } from "../getTranslation";
import { imageBaseUrl } from '@constants/envVariables';
import CardInfo from "components/cardInfo/CardInfo";
import styles from "./page.module.scss";
import data from '../../../data.json';


// You now have access to the current locale
// e.g. /en-US/products -> `lang` is "en-US"
export default async function TestPage({
  params
}: {
  params: Promise<{ lang: "en"|"ar" }>
}) {
  const { lang } = await params;
  console.log('lang', lang);
  // const translations = await getTranslation(lang) // en

  return (
    <div className={styles['container']}>
        <CardInfo data={data[0]} />
    </div>
  );
}
