import CommonLayout from "components/common/commonLayout/CommonLayout";
import cardsData from "components/home/<USER>/cardsData.json";


export async function generateMetadata({
  params,
}: {
  params: any;
}) {
  let metaInfo: any = {title : "", description : ""};

  try {
    const slugInfo = (await params).slug;
    const cardSlug = slugInfo[0];
    const buyerCardInfo = cardsData.buyers[0]['cards'].find((card: any) => {
      return card.slug === cardSlug
    });
    if (buyerCardInfo) {
      metaInfo = buyerCardInfo?.meta;
    } else {
      const brandCardInfo = cardsData.brands[0]['cards'].find((card: any) => card.slug === cardSlug);
      if (brandCardInfo) {
        metaInfo = brandCardInfo?.meta;
      }

    }
  } catch(error){
    console.error('Error fetching metadata:', error);
  }

  
 
  return {
    title: metaInfo?.title,
    description: metaInfo?.description,
  }
}

export default function ProductLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return <CommonLayout>{children}</CommonLayout>;
}
