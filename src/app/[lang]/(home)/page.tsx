import CardsComponent from "components/home/<USER>/CardsComponent";
import Header from "components/common/header/Header";
import Banner from "components/home/<USER>/Banner";
import styles from "./page.module.css";
import LandingMweb from "features/mweb/LandingMweb";

// You now have access to the current locale
// e.g. /en-US/products -> `lang` is "en-US"
export default async function HomePage({
  params,
}: {
  params: Promise<{ lang: "en" | "ar" }>;
}) {
  const { lang } = await params;

  return (
    <>
    <div className={styles.main}>
      <div className={styles.headerSection}>
        <Header />
        <Banner />
      </div>
      <div className={styles.contentSection}>
        <CardsComponent />
      </div>
    </div>
    <div className={styles['mweb-main']}>
      <LandingMweb/>
    </div>
    </>
  );
}
