@import "@styles/variables";
@import "@styles/mixins";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: $default-font-family;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  & [dir="rtl"] {
    font-family: $arabic-font-family;
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

.container {
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: ($lg)) {
    max-width: 1100px;
  }
  @media (max-width: ($md + 150)) {
    max-width: 900px;
  }
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}
.exit-animation {
	transform: translateX(30px);
	filter: blur(8px);
	transition: 
		transform 1s cubic-bezier(0.4, 0, 0.2, 1),
		filter 1s cubic-bezier(0.4, 0, 0.2, 1);
	pointer-events: none;
	will-change: transform, filter;
}

.enter-animation {
	transform: translateX(0);
	filter: blur(0);
	transition:
		transform 1s cubic-bezier(0.4, 0, 0.2, 1),
		filter 1s cubic-bezier(0.4, 0, 0.2, 1);
	pointer-events: none;
	will-change: transform, filter;
}

.country-phone-code {
  -ms-overflow-style: none;
  scrollbar-width: none;

  .MuiOutlinedInput-root {
    border: none !important;
    padding: 0 !important;
  }

  .MuiInputAdornment-root {
    margin-right: 0 !important;
  }

  .MuiSelect-icon {
    right: 0;

    @include rtl-styles {
      right: auto;
      left: -7px;
    }
  }
  .MuiOutlinedInput-input {
    padding: 0 24px 0 0 !important;

    @include rtl-styles {
      padding: 0 0 0 15px !important;
    }
  }

  ::placeholder {
    opacity: 0.6;
  }

  ::-webkit-scrollbar {
    display: none;
  }

  ul {
    li.Mui-selected {
      background-color: $grey-primary !important;
    }
  }

  div {
    padding-top: 0;
    padding-bottom: 0;
    align-items: center;
  }

  .country-phone-code-flag {
    width: 24px;
    height: 24px;
    font-size: 24px;
    border-radius: 50px;
    overflow: hidden;
  }
}

.paper-signin-dropdowns {
  border-radius: 12px !important;
  border: 1px solid $semi-dark-grey1 !important;
  box-shadow: none !important;
  margin-top: 20px;
  margin-left: -45px;
  font-family: $default-font-family !important;

  @include rtl-styles {
    margin-left: -84px;
  }

  ul {
    padding: 0;
    width: 100%;
  }

  // max-height: 300px !important;
  width: 360px;
  height: 300px !important;
}

.formContainer {
  .MuiFormHelperText-root {
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 150% */
    letter-spacing: -0.12px;
    font-family: $default-font-family;
    color: #9d1616;
    margin: 0;
    margin-top: 8px;
  }
}

.textfield-mobile {
  border: none !important;
  @include rtl-styles {
    padding-left: 20px;
  }
}

.modal {
  &__backdrop {
    background-color: rgba(0, 0, 0, 0.3) !important;
    position: relative;
  }

  &__content {
    position: absolute;
    bottom: 40px;
    right: 40px;
    max-height: calc(100vh - 86px);
    outline: none;
    animation: slideInFromLeft 0.5s ease-out forwards;
    overflow-y: auto;

    @media (max-width: 768px) {
      right: 20px;
      bottom: 20px;
      max-width: calc(100vw - 40px);

      @include rtl-styles {
        right: auto;
        left: 20px;
      }
    }

    @media (max-width: 576px) {
      right: 10px;
      bottom: 10px;
      max-width: calc(100vw - 20px);

      @include rtl-styles {
        right: auto;
        left: 10px;
      }
    }

    &::-webkit-scrollbar {
      display: none;
    }
  }
}

@keyframes slideInFromLeft {
  0% {
    transform: translate(100%);
    opacity: 0;
  }
  100% {
    transform: translate(0%);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  0% {
    transform: translate(-100%);
    opacity: 0;
  }
  100% {
    transform: translate(0%);
    opacity: 1;
  }
}


.component-transition {
  opacity: 0;
  animation: fadeIn 0.5s ease-in forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

.rfm-marquee {
  @include rtl-styles {
    @keyframes arabic-scroll {
      0% {
        transform: translateX(100%);
      }

      100% {
        transform: translateX(0%);
      }
    }

    animation: arabic-scroll var(--duration) linear var(--delay)
      var(--iteration-count);
    animation-play-state: var(--play);
    animation-delay: var(--delay);
    animation-direction: var(--direction);
    overflow-x: hidden;
  }
  overflow: hidden;
  .rfm-child {
    overflow: hidden;
  }
}
