import { i18nRouter } from "next-i18n-router";
import { NextResponse } from "next/server";
import i18nConfig from "../i18nConfig";

const locales = ['en', 'ar'];
const defaultLocale = 'en';
const LOCALE_COOKIE_NAME = 'NEXT_LOCALE';

/**
 * Gets the locale from the cookie if present
 * @param request - The incoming request
 * @returns The locale from cookie or default locale
 */
const getLocaleFromCookie = (request: any): string => {
  const cookieLocale = request.cookies.get(LOCALE_COOKIE_NAME)?.value;
  return locales.includes(cookieLocale) ? cookieLocale : defaultLocale;
};

/**
 * Sets the locale cookie
 * @param response - The response object
 * @param locale - The locale to set
 */
const setLocaleCookie = (response: NextResponse, locale: string) => {
  response.cookies.set(LOCALE_COOKIE_NAME, locale, {
    path: '/',
    maxAge: 60 * 60 * 24 * 30,
    sameSite: 'lax',
  });
};

export function middleware(request: any) {
  const { pathname } = request.nextUrl;

  const basePath = '/solutions-hub';
  const relativePath = pathname.replace(basePath, '') || '/';

  if (relativePath === '/') {
    const locale = defaultLocale; 
    const url = request.nextUrl.clone();
    url.pathname = `/${locale}`;
    return NextResponse.redirect(url);
  }

  // Ignore the following paths
  if (pathname.endsWith('.svg') || pathname.endsWith('.png')) {
    return;
  }

  const pathnameHasLocale = locales.some(
    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );

  console.log('pathnameHasLocale ', pathnameHasLocale);

  if (pathnameHasLocale) {
    // If locale is in URL, update the cookie
    const localeFromUrl = pathname.split('/')[1];
    const response = NextResponse.next();
    setLocaleCookie(response, localeFromUrl);
    return response;
  }

  // Get locale from cookie or use default
  const locale = getLocaleFromCookie(request);
  
  // Create a new URL with the locale
  const newUrl = new URL(request.url);
  newUrl.pathname = `/${locale}${pathname}`;
  console.log('pathname ', pathname);
  console.log('new url ', newUrl);
  // Create a redirect response
  const response = NextResponse.redirect(newUrl);
  
  // Set the cookie on the redirect response
  setLocaleCookie(response, locale);
  
  return response;
}

export const config = {
    matcher: [
        /*
         * Match all request paths except for the ones starting with:
         * - api (API routes)
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico, sitemap.xml, robots.txt (metadata files)
         */
        '/((?!api|_next/static|_next/image|health|favicon.ico|sitemap.xml|robots.txt).*)','/'
      ],
  }