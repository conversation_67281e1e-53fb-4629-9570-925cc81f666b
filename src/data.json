[{"id": 1, "slug": "gift-card-api", "name": "giftCardAPI", "features": [{"id": 1, "name": "theTechnology", "slug": "technology", "icon": "setting.svg", "icon-inactive": "setting-inactive.svg", "body": {"title": "theTechnology", "subTitle": "apiTechDesc", "performaceUptime": {"title": "apiPerformance", "slideData": [{"image": "graph.svg", "title": "99.999%", "subTitle": "uptimesla"}, {"image": "avg-api.svg", "title": "<500ms", "subTitle": "avgapiresponse"}, {"image": "concurrent.svg", "title": "5,000+", "subTitle": "concurrentrequests"}, {"image": "daily-up.svg", "title": "1M+", "subTitle": "dailyapicallserved"}, {"image": "stars.svg", "title": ">99.99%", "subTitle": "requestsuccessrate"}]}, "security": {"title": "security", "slideData": [{"image": "pci.svg", "title": "pciDSS", "subTitle": "compliant"}, {"image": "oauth.svg", "title": "o<PERSON>h", "subTitle": "jwtAuth"}, {"image": "end.svg", "title": "endtoend", "subTitle": "encryption"}, {"image": "whitelisting.svg", "title": "ipw<PERSON><PERSON><PERSON>", "subTitle": "enhancedSecurity"}]}}}, {"id": 2, "name": "useCasesFeatures", "slug": "features", "icon": "arrow.svg", "icon-inactive": "arrow-inactive.svg", "body": {"title": "useCasesFeatures", "subTitle": "useCasesDesc", "chipsNTitle1": {"title": "apiusecases", "chips": ["loyaltyPrograms", "incentivePrograms", "marketplace", "rewards&Recognition", "resellers", "aggregators", "fintechs&SuperApps", "payment&Wallets"]}, "chipsNTitle2": {"title": "apifeatures", "chips": ["country", "currency", "category", "brands", "accountManagement", "orderManagement", "deliveryManagement"]}}}, {"id": 3, "name": "ourBrandNetwork", "slug": "brandNetwork", "icon": "brandnetwork.svg", "icon-inactive": "brandnetwork-inactive.svg", "body": {"title": "ourBrandNetwork", "subTitle": "ourBrandDesc", "chips1": ["6countries", "19categories", "5currencies"], "chipsNTitle": {"title": "regionalBrandCoverage", "chips": ["210+uaebrands", "150+ksabrands", "110qatarbrands", "500japanbrands", "300othergccbrands", "800restbrands"]}, "titleImage": {"title": "popularRegionalBrands", "images": ["noon.svg", "amazon.svg", "carefour.svg", "hungerstation.svg", "lulu.svg", "panda.svg", "ig.svg", "ikea.svg", "love.svg", "sharafdg.svg", "centerpoint.svg"]}}}, {"id": 4, "name": "guidedExp", "slug": "experience", "icon": "guidedexp.svg", "icon-inactive": "guidedexp-inactive.svg", "body": {"title": "guidedExp", "subTitle": "guidedExpDesc", "chips": ["dashboards", "sandbox", "integrationSpeed", "postmanCollection", "logNReport", "swaggerUI"]}}, {"id": 5, "name": "ourAPIClient", "slug": "ourAPIClient", "icon": "ourclient.svg", "icon-inactive": "ourclient-inactive.svg", "body": {"title": "ourAPIClient", "subTitle": "apiClientDesc"}}]}, {"id": 4, "slug": "gifting-and-wallet", "name": "giftingWallet", "features": [{"id": 1, "name": "giftCatalog", "icon": "giftCatalog.svg", "slug": "giftCatalog", "icon-inactive": "giftCatalog-inactive.svg", "body": {"title": "giftCatalog", "subTitle": "giftCatalogDesc", "chips": ["categoryWiseListing", "customDenominations", "multiBrandSupport"], "image": "/images/giftCardsMweb.png"}}, {"id": 2, "name": "individualGifting", "icon": "profile-card.svg", "slug": "individualGifting", "icon-inactive": "profile-card-inactive.svg", "body": {"title": "individualGifting", "subTitle": "individualGiftingDesc", "chips": ["easyGifting", "digitalGifting"], "image": "/images/brandPageMweb.svg"}}, {"id": 3, "name": "groupGifting", "icon": "user-group.svg", "slug": "groupGifting", "icon-inactive": "user-group-inactive.svg", "body": {"title": "groupGifting", "subTitle": "groupGiftingDesc", "chips": ["giftTogether", "personalisedGifting", "flexibleContribution"], "image": "/images/ggMweb.svg"}}, {"id": 4, "name": "personalisation", "icon": "edit-user.svg", "slug": "personalisation", "icon-inactive": "edit-user-inactive.svg", "body": {"title": "personalisation", "subTitle": "perosonalizationDesc", "chips": ["customGifts", "personalisedGift"], "image": "/images/add-Media.svg"}}, {"id": 5, "name": "wallet", "icon": "wallet.svg", "slug": "wallet", "icon-inactive": "wallet-inactive.svg", "body": {"title": "wallet", "subTitle": "walletDesc", "chips": ["giftingHub", "organisedGifting", "trackGifts"], "image": "/images/walletMweb.svg"}}]}, {"id": 3, "slug": "atwork", "name": "atWork", "features": [{"id": 1, "name": "designedForBusiness", "slug": "business", "icon": "business-active.svg", "icon-inactive": "business-inactive.svg", "body": {"title": "designedForBusiness", "subTitle": "businessDesc", "chips": ["asistedSales", "bulkOrder", "customDesigns"]}}, {"id": 2, "name": "bulkOrder", "slug": "bulkOrder", "icon": "bulk-order-active.svg", "icon-inactive": "bulk-order-inactive.svg", "body": {"title": "bulkOrder", "subTitle": "atWorkDesc", "chips": ["printAtHome", "emailSms", "whatsApp"]}}, {"id": 3, "name": "customDesigns", "slug": "customDesign", "icon": "custom-active.svg", "icon-inactive": "custom-inactive.svg", "body": {"title": "customDesigns", "subTitle": "customDesc", "image": "custom-designs.png"}}, {"id": 4, "name": "asistedSales", "slug": "assistedSales", "icon": "asisted-active.svg", "icon-inactive": "asisted-inactive.svg", "body": {"title": "asistedSales", "subTitle": "assistesDesc", "image": "assisted-sales.png"}}, {"id": 5, "name": "advancedFeature", "slug": "workPro", "icon": "work-pro-active.svg", "icon-inactive": "work-pro-inactive.svg", "body": {"title": "atWorkPro", "subTitle": "proDesc", "chips": ["acountManagement", "insights", "personalise"], "image": "work-pro.png"}}]}, {"id": 2, "slug": "giftshop", "name": "giftShop", "features": [{"id": 1, "name": "theTechnology", "slug": "technology", "icon": "setting.svg", "icon-inactive": "setting-inactive.svg", "body": {"title": "theTechnology", "subTitle": "giftShopTechDesc", "security": {"title": "security", "slideData": [{"image": "pci.svg", "title": "pciDSS", "subTitle": "compliant"}, {"image": "oauth.svg", "title": "o<PERSON>h", "subTitle": "jwtAuth"}, {"image": "end.svg", "title": "endtoend", "subTitle": "encryption"}, {"image": "whitelisting.svg", "title": "ipw<PERSON><PERSON><PERSON>", "subTitle": "enhancedSecurity"}]}}}, {"id": 2, "name": "useCasesFeatures", "slug": "features", "icon": "arrow.svg", "icon-inactive": "arrow-inactive.svg", "body": {"title": "useCasesFeatures", "subTitle": "useCasesDesc", "chipsNTitle1": {"title": "giftShopUseCases", "chips": ["customerRewards", "marketplaces", "salesIncentives"]}, "chipsNTitle2": {"title": "giftShopFeatures", "chips": ["whiteLabelled", "responsiveDesign", "integratedGiftCardCatalog", "multiPayment", "deliveryManagement", "adminControls"]}}}, {"id": 3, "name": "loyaltyPoints", "slug": "loyaltyPoints", "icon": "loyal.svg", "icon-inactive": "loyal-inactive.svg", "body": {"title": "loyaltyPoints", "subTitle": "loyaltyPointsDesc", "chips": ["balancePoints", "buyGiftPoints", "orderTracking"]}}, {"id": 4, "name": "ourGiftShopClient", "slug": "ourGiftShopClient", "icon": "gift-client.svg", "icon-inactive": "gift-client-inactive.svg", "body": {"title": "ourGiftShopClient", "subTitle": "apiClientDesc"}}]}, {"id": 5, "slug": "campaign-management", "name": "campaignManagement", "features": [{"id": 1, "name": "configurablePromotions", "slug": "configurablePromotions", "icon": "config-promo.svg", "icon-inactive": "config-promo-inactive.svg", "body": {"title": "configurablePromotions", "subTitle": "configPromoDesc", "chips": ["invoiceBasedPromotions", "timeBoundOffers", "budgetControlledPromotions"]}}, {"id": 2, "name": "categorySpecificCampaigns", "slug": "categorySpecificCampaigns", "icon": "cat-campaign.svg", "icon-inactive": "cat-campaign-inactive.svg", "body": {"title": "categorySpecificCampaigns", "subTitle": "categoryCampDesc"}}, {"id": 3, "name": "conditionalVouchers", "slug": "conditionalVouchers", "icon": "voucher.svg", "icon-inactive": "voucher-inactive.svg", "body": {"title": "conditionalVouchers", "subTitle": "voucherDesc"}}]}, {"id": 6, "slug": "value-added-services", "name": "valueAdded", "features": [{"id": 1, "name": "whiteLabelGiftCardMicrosite", "slug": "white-label-gift-card-microsite", "icon": "white-label.svg", "icon-inactive": "white-lable-inactive.svg", "body": {"title": "whiteLabelGiftCardMicrosite", "subTitle": "whiteLabelDesc", "chips": ["brandCustomisedInterface", "giftingExperience", "seamlessCheckoutExperience", "supportsMultipleGiftCard"]}}, {"id": 2, "name": "mpos", "slug": "mpos", "icon": "mposs.svg", "icon-inactive": "mpos-inactive.svg", "body": {"title": "mpos", "subTitle": "mposDesc", "chips": ["inStoreIssuanceAndRedemption", "customerServiceCounters", "eventsOrKiosks"]}}, {"id": 3, "name": "corporateSalesPortal", "slug": "corporate-sales-portal", "icon": "corporate.svg", "icon-inactive": "corporate-inactive.svg", "body": {"title": "corporateSales", "subTitle": "corporateSalesDesc", "chips": ["corporateWalletManagement", "tieredDiscountStructures", "orderTrackingAndReporting", "accessControlledLogins"]}}, {"id": 4, "name": "thirdPartyIntegrations", "slug": "third-party-integrations", "icon": "third.svg", "icon-inactive": "third-inactive.svg", "body": {"title": "thirdPartyIntegrations", "subTitle": "thirdPartyDesc", "chips": ["restApi", "onboarding", "affiliateAndLoyaltyPartners"]}}]}, {"id": 7, "slug": "program-management", "name": "programManagement", "features": [{"id": 1, "name": "programSetup", "slug": "programSetup", "icon": "program.svg", "icon-inactive": "program-inactive.svg", "body": {"title": "programSetup", "subTitle": "programSetUpDesc", "chips": ["endToEndProgramSetup", "customBrandedSolutions", "scalableAndModularArchitecture"]}}, {"id": 2, "name": "centralisedControlAndConfiguration", "slug": "centralisedControlAndConfiguration", "icon": "central.svg", "icon-inactive": "central-inactive.svg", "body": {"title": "centralisedControlAndConfiguration", "subTitle": "centralDesc", "chips": ["centralisedProgramControl", "rulesAndConfigurationEngine", "multiChannelEnablement"]}}, {"id": 3, "name": "reportingAndSecurity", "slug": "reportingAndSecurity", "icon": "report.svg", "icon-inactive": "report-inactive.svg", "body": {"title": "reportingAndSecurity", "subTitle": "reportDesc", "chips": ["realTimeDashboards", "reportingAndReconciliation", "fraudAndRiskManagement"]}}, {"id": 4, "name": "corporateAndMerchantPortals", "slug": "corporateAndMerchantPortals", "icon": "coporate.svg", "icon-inactive": "coporate-inactive.svg", "body": {"title": "corporateAndMerchantPortals", "subTitle": "coporateDesc", "chips": ["salesPortal", "b2bOrderManagement", "bulkIssuanceTools", "thirdPartyIntegrations"]}}, {"id": 5, "name": "customerSupportAndServiceTools", "slug": "customerSupportAndServiceTools", "icon": "customer.svg", "icon-inactive": "customer-inactive.svg", "body": {"title": "customerSupportAndServiceTools", "subTitle": "customerDesc", "chips": ["customerSupportTools", "whiteLabelGiftCardEcommerce", "giftingExperience", "merchantAndCorporatePortalAccess"]}}]}, {"id": 8, "slug": "card-products", "name": "cardProducts", "features": [{"id": 1, "name": "giftCards", "slug": "giftCards", "icon": "gift.svg", "icon-inactive": "gift-inactive.svg", "body": {"title": "giftCards", "subTitle": "giftCardDesc", "chips": ["singleOrMultiBrandSupport", "configurableDenominationAndValidity", "fullOmnichannelCompatibility", "secureIssuanceWithRealTimeTracking"]}}, {"id": 2, "name": "singleAndMultiBrandCards", "slug": "singleAndMultiBrandCards", "icon": "single.svg", "icon-inactive": "single-inactive.svg", "body": {"title": "singleAndMultiBrandCards", "subTitle": "singleMultiDesc", "chips": ["singleBrandGiftCards", "multiBrandGiftCards", "retailGroupCards", "corporateGroupCards"]}}, {"id": 3, "name": "closedLoopCards", "slug": "closedLoopCards", "icon": "closed.svg", "icon-inactive": "closed-inactive.svg", "body": {"title": "closedLoopCards", "subTitle": "closedDesc", "chips": ["storeOnlyGiftCards", "omnichannelGiftCards", "multiBrandSingleNetworkCards", "corporateB2BGiftCards", "topUpReloadableCards"]}}, {"id": 4, "name": "openLoopCards", "slug": "openLoopCards", "icon": "open.svg", "icon-inactive": "open-inactive.svg", "body": {"title": "openLoopCards", "subTitle": "openDesc", "chips": ["universalAcceptance", "restrictedOpenLoopCards", "corporateIncentiveCards", "reloadableNonReloadable", "compliance"]}}, {"id": 5, "name": "promoCards", "slug": "promoCards", "icon": "promo.svg", "icon-inactive": "promo-inactive.svg", "body": {"title": "promoCards", "subTitle": "promoDesc", "chips": ["acquisitionFirstPurchaseOffers", "cashbackSpendBackCampaigns", "refundStoreCreditCards", "timeBoundVouchers", "conditionalUseCases"]}}, {"id": 6, "name": "wallet", "slug": "wallet", "icon": "wallet-card.svg", "icon-inactive": "wallet-card-inactive.svg", "body": {"title": "wallet", "subTitle": "walletCardDesc", "chips": ["reloadableWalletForRepeatSpend", "refundsStoreCredit", "multiChannelBrandControlledUsage", "customRulesRealTimeReporting", "idealForPromotionsLoyalty"]}}]}]